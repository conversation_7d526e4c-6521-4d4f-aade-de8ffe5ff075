{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/src/hooks/use-mobile.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nconst MO<PERSON>LE_BREAKPOINT = 768\n\nexport function useIsMobile() {\n  const [isMobile, setIsMobile] = React.useState<boolean | undefined>(undefined)\n\n  React.useEffect(() => {\n    const mql = window.matchMedia(`(max-width: ${MOBILE_BREAKPOINT - 1}px)`)\n    const onChange = () => {\n      setIsMobile(window.innerWidth < MOBILE_BREAKPOINT)\n    }\n    mql.addEventListener(\"change\", onChange)\n    setIsMobile(window.innerWidth < MOBILE_BREAKPOINT)\n    return () => mql.removeEventListener(\"change\", onChange)\n  }, [])\n\n  return !!isMobile\n}\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,oBAAoB;AAEnB,SAAS;IACd,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAc,AAAD,EAAuB;IAEpE,CAAA,GAAA,qMAAA,CAAA,YAAe,AAAD,EAAE;QACd,MAAM,MAAM,OAAO,UAAU,CAAC,CAAC,YAAY,EAAE,oBAAoB,EAAE,GAAG,CAAC;QACvE,MAAM,WAAW;YACf,YAAY,OAAO,UAAU,GAAG;QAClC;QACA,IAAI,gBAAgB,CAAC,UAAU;QAC/B,YAAY,OAAO,UAAU,GAAG;QAChC,OAAO,IAAM,IAAI,mBAAmB,CAAC,UAAU;IACjD,GAAG,EAAE;IAEL,OAAO,CAAC,CAAC;AACX", "debugId": null}}, {"offset": {"line": 32, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/src/components/ui/button.tsx"], "sourcesContent": ["\nimport * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap truncate rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0\",\n  {\n    variants: {\n      variant: {\n        default: \"bg-primary text-primary-foreground hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-destructive-foreground hover:bg-destructive/90\",\n        outline:\n          \"border border-input bg-background hover:bg-accent hover:text-accent-foreground\",\n        secondary:\n          \"bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n        ghost: \"hover:bg-accent hover:text-accent-foreground\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-10 px-4 py-2\",\n        sm: \"h-9 rounded-md px-3\",\n        lg: \"h-11 rounded-md px-8\",\n        icon: \"h-10 w-10\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nexport interface ButtonProps\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\n    VariantProps<typeof buttonVariants> {\n  asChild?: boolean\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant, size, asChild = false, ...props }, ref) => {\n    const Comp = asChild ? Slot : \"button\"\n    return (\n      <Comp\n        className={cn(buttonVariants({ variant, size, className }))}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nButton.displayName = \"Button\"\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AACA;AAEA;;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACvB,qWACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aACE;YACF,SACE;YACF,WACE;YACF,OAAO;YACP,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AASF,MAAM,uBAAS,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAC5B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,UAAU,KAAK,EAAE,GAAG,OAAO,EAAE;IACxD,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAC9B,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACxD,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AAEF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 92, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Input = React.forwardRef<HTMLInputElement, React.ComponentProps<\"input\">>(\n  ({ className, type, ...props }, ref) => {\n    return (\n      <input\n        type={type}\n        className={cn(\n          \"flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\n          className\n        )}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nInput.displayName = \"Input\"\n\nexport { Input }\n"], "names": [], "mappings": ";;;;AAAA;AAEA;;;;AAEA,MAAM,sBAAQ,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAC3B,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAO,EAAE;IAC9B,qBACE,8OAAC;QACC,MAAM;QACN,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kYACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AAEF,MAAM,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 121, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/src/components/ui/separator.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as SeparatorPrimitive from \"@radix-ui/react-separator\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Separator = React.forwardRef<\n  React.ElementRef<typeof SeparatorPrimitive.Root>,\n  React.ComponentPropsWithoutRef<typeof SeparatorPrimitive.Root>\n>(\n  (\n    { className, orientation = \"horizontal\", decorative = true, ...props },\n    ref\n  ) => (\n    <SeparatorPrimitive.Root\n      ref={ref}\n      decorative={decorative}\n      orientation={orientation}\n      className={cn(\n        \"shrink-0 bg-border\",\n        orientation === \"horizontal\" ? \"h-[1px] w-full\" : \"h-full w-[1px]\",\n        className\n      )}\n      {...props}\n    />\n  )\n)\nSeparator.displayName = SeparatorPrimitive.Root.displayName\n\nexport { Separator }\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAEA;AALA;;;;;AAOA,MAAM,0BAAY,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAI/B,CACE,EAAE,SAAS,EAAE,cAAc,YAAY,EAAE,aAAa,IAAI,EAAE,GAAG,OAAO,EACtE,oBAEA,8OAAC,qKAAA,CAAA,OAAuB;QACtB,KAAK;QACL,YAAY;QACZ,aAAa;QACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,sBACA,gBAAgB,eAAe,mBAAmB,kBAClD;QAED,GAAG,KAAK;;;;;;AAIf,UAAU,WAAW,GAAG,qKAAA,CAAA,OAAuB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 152, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/src/components/ui/sheet.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as SheetPrimitive from \"@radix-ui/react-dialog\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\nimport { X } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Sheet = SheetPrimitive.Root\n\nconst SheetTrigger = SheetPrimitive.Trigger\n\nconst SheetClose = SheetPrimitive.Close\n\nconst SheetPortal = SheetPrimitive.Portal\n\nconst SheetOverlay = React.forwardRef<\n  React.ElementRef<typeof SheetPrimitive.Overlay>,\n  React.ComponentPropsWithoutRef<typeof SheetPrimitive.Overlay>\n>(({ className, ...props }, ref) => (\n  <SheetPrimitive.Overlay\n    className={cn(\n      \"fixed inset-0 z-50 bg-black/80  data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0\",\n      className\n    )}\n    {...props}\n    ref={ref}\n  />\n))\nSheetOverlay.displayName = SheetPrimitive.Overlay.displayName\n\nconst sheetVariants = cva(\n  \"fixed z-50 gap-4 bg-background p-6 shadow-lg transition ease-in-out data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:duration-300 data-[state=open]:duration-500\",\n  {\n    variants: {\n      side: {\n        top: \"inset-x-0 top-0 border-b data-[state=closed]:slide-out-to-top data-[state=open]:slide-in-from-top\",\n        bottom:\n          \"inset-x-0 bottom-0 border-t data-[state=closed]:slide-out-to-bottom data-[state=open]:slide-in-from-bottom\",\n        left: \"inset-y-0 left-0 h-full w-3/4 border-r data-[state=closed]:slide-out-to-left data-[state=open]:slide-in-from-left sm:max-w-sm\",\n        right:\n          \"inset-y-0 right-0 h-full w-3/4  border-l data-[state=closed]:slide-out-to-right data-[state=open]:slide-in-from-right sm:max-w-sm\",\n      },\n    },\n    defaultVariants: {\n      side: \"right\",\n    },\n  }\n)\n\ninterface SheetContentProps\n  extends React.ComponentPropsWithoutRef<typeof SheetPrimitive.Content>,\n    VariantProps<typeof sheetVariants> {}\n\nconst SheetContent = React.forwardRef<\n  React.ElementRef<typeof SheetPrimitive.Content>,\n  SheetContentProps\n>(({ side = \"right\", className, children, ...props }, ref) => (\n  <SheetPortal>\n    <SheetOverlay />\n    <SheetPrimitive.Content\n      ref={ref}\n      className={cn(sheetVariants({ side }), className)}\n      {...props}\n    >\n      {children}\n      <SheetPrimitive.Close className=\"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-secondary\">\n        <X className=\"h-4 w-4\" />\n        <span className=\"sr-only\">Close</span>\n      </SheetPrimitive.Close>\n    </SheetPrimitive.Content>\n  </SheetPortal>\n))\nSheetContent.displayName = SheetPrimitive.Content.displayName\n\nconst SheetHeader = ({\n  className,\n  ...props\n}: React.HTMLAttributes<HTMLDivElement>) => (\n  <div\n    className={cn(\n      \"flex flex-col space-y-2 text-center sm:text-left\",\n      className\n    )}\n    {...props}\n  />\n)\nSheetHeader.displayName = \"SheetHeader\"\n\nconst SheetFooter = ({\n  className,\n  ...props\n}: React.HTMLAttributes<HTMLDivElement>) => (\n  <div\n    className={cn(\n      \"flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2\",\n      className\n    )}\n    {...props}\n  />\n)\nSheetFooter.displayName = \"SheetFooter\"\n\nconst SheetTitle = React.forwardRef<\n  React.ElementRef<typeof SheetPrimitive.Title>,\n  React.ComponentPropsWithoutRef<typeof SheetPrimitive.Title>\n>(({ className, ...props }, ref) => (\n  <SheetPrimitive.Title\n    ref={ref}\n    className={cn(\"text-lg font-semibold text-foreground\", className)}\n    {...props}\n  />\n))\nSheetTitle.displayName = SheetPrimitive.Title.displayName\n\nconst SheetDescription = React.forwardRef<\n  React.ElementRef<typeof SheetPrimitive.Description>,\n  React.ComponentPropsWithoutRef<typeof SheetPrimitive.Description>\n>(({ className, ...props }, ref) => (\n  <SheetPrimitive.Description\n    ref={ref}\n    className={cn(\"text-sm text-muted-foreground\", className)}\n    {...props}\n  />\n))\nSheetDescription.displayName = SheetPrimitive.Description.displayName\n\nexport {\n  Sheet,\n  SheetPortal,\n  SheetOverlay,\n  SheetTrigger,\n  SheetClose,\n  SheetContent,\n  SheetHeader,\n  SheetFooter,\n  SheetTitle,\n  SheetDescription,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAEA;AACA;AACA;AACA;AAEA;AAPA;;;;;;;AASA,MAAM,QAAQ,kKAAA,CAAA,OAAmB;AAEjC,MAAM,eAAe,kKAAA,CAAA,UAAsB;AAE3C,MAAM,aAAa,kKAAA,CAAA,QAAoB;AAEvC,MAAM,cAAc,kKAAA,CAAA,SAAqB;AAEzC,MAAM,6BAAe,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGlC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAA,CAAA,UAAsB;QACrB,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,2JACA;QAED,GAAG,KAAK;QACT,KAAK;;;;;;AAGT,aAAa,WAAW,GAAG,kKAAA,CAAA,UAAsB,CAAC,WAAW;AAE7D,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACtB,oMACA;IACE,UAAU;QACR,MAAM;YACJ,KAAK;YACL,QACE;YACF,MAAM;YACN,OACE;QACJ;IACF;IACA,iBAAiB;QACf,MAAM;IACR;AACF;AAOF,MAAM,6BAAe,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGlC,CAAC,EAAE,OAAO,OAAO,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpD,8OAAC;;0BACC,8OAAC;;;;;0BACD,8OAAC,kKAAA,CAAA,UAAsB;gBACrB,KAAK;gBACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;oBAAE;gBAAK,IAAI;gBACtC,GAAG,KAAK;;oBAER;kCACD,8OAAC,kKAAA,CAAA,QAAoB;wBAAC,WAAU;;0CAC9B,8OAAC,4LAAA,CAAA,IAAC;gCAAC,WAAU;;;;;;0CACb,8OAAC;gCAAK,WAAU;0CAAU;;;;;;;;;;;;;;;;;;;;;;;;AAKlC,aAAa,WAAW,GAAG,kKAAA,CAAA,UAAsB,CAAC,WAAW;AAE7D,MAAM,cAAc,CAAC,EACnB,SAAS,EACT,GAAG,OACkC,iBACrC,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,oDACA;QAED,GAAG,KAAK;;;;;;AAGb,YAAY,WAAW,GAAG;AAE1B,MAAM,cAAc,CAAC,EACnB,SAAS,EACT,GAAG,OACkC,iBACrC,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,iEACA;QAED,GAAG,KAAK;;;;;;AAGb,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAA,CAAA,QAAoB;QACnB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,yCAAyC;QACtD,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG,kKAAA,CAAA,QAAoB,CAAC,WAAW;AAEzD,MAAM,iCAAmB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGtC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAA,CAAA,cAA0B;QACzB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,iBAAiB,WAAW,GAAG,kKAAA,CAAA,cAA0B,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 301, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/src/components/ui/skeleton.tsx"], "sourcesContent": ["import { cn } from \"@/lib/utils\"\n\nfunction Skeleton({\n  className,\n  ...props\n}: React.HTMLAttributes<HTMLDivElement>) {\n  return (\n    <div\n      className={cn(\"animate-pulse rounded-md bg-muted\", className)}\n      {...props}\n    />\n  )\n}\n\nexport { Skeleton }\n"], "names": [], "mappings": ";;;;AAAA;;;AAEA,SAAS,SAAS,EAChB,SAAS,EACT,GAAG,OACkC;IACrC,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,qCAAqC;QAClD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 325, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/src/components/ui/tooltip.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as TooltipPrimitive from \"@radix-ui/react-tooltip\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst TooltipProvider = TooltipPrimitive.Provider\n\nconst Tooltip = TooltipPrimitive.Root\n\nconst TooltipTrigger = TooltipPrimitive.Trigger\n\nconst TooltipContent = React.forwardRef<\n  React.ElementRef<typeof TooltipPrimitive.Content>,\n  React.ComponentPropsWithoutRef<typeof TooltipPrimitive.Content>\n>(({ className, sideOffset = 4, ...props }, ref) => (\n  <TooltipPrimitive.Content\n    ref={ref}\n    sideOffset={sideOffset}\n    className={cn(\n      \"z-50 overflow-hidden rounded-md border bg-popover px-3 py-1.5 text-sm text-popover-foreground shadow-md animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2\",\n      className\n    )}\n    {...props}\n  />\n))\nTooltipContent.displayName = TooltipPrimitive.Content.displayName\n\nexport { Tooltip, TooltipTrigger, TooltipContent, TooltipProvider }\n"], "names": [], "mappings": ";;;;;;;AAEA;AACA;AAEA;AALA;;;;;AAOA,MAAM,kBAAkB,mKAAA,CAAA,WAAyB;AAEjD,MAAM,UAAU,mKAAA,CAAA,OAAqB;AAErC,MAAM,iBAAiB,mKAAA,CAAA,UAAwB;AAE/C,MAAM,+BAAiB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGpC,CAAC,EAAE,SAAS,EAAE,aAAa,CAAC,EAAE,GAAG,OAAO,EAAE,oBAC1C,8OAAC,mKAAA,CAAA,UAAwB;QACvB,KAAK;QACL,YAAY;QACZ,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,sYACA;QAED,GAAG,KAAK;;;;;;AAGb,eAAe,WAAW,GAAG,mKAAA,CAAA,UAAwB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 361, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/src/components/ui/sidebar.tsx"], "sourcesContent": ["\n\"use client\"\n\nimport * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { VariantProps, cva } from \"class-variance-authority\"\nimport { ChevronsLeft, ChevronsRight, Menu } from \"lucide-react\"\n\nimport { useIsMobile } from \"@/hooks/use-mobile\"\nimport { cn } from \"@/lib/utils\"\nimport { Button } from \"@/components/ui/button\"\nimport { Input } from \"@/components/ui/input\"\nimport { Separator } from \"@/components/ui/separator\"\nimport { Sheet, SheetContent } from \"@/components/ui/sheet\"\nimport { Skeleton } from \"@/components/ui/skeleton\"\nimport {\n  <PERSON><PERSON><PERSON>,\n  TooltipContent,\n  TooltipProvider,\n  TooltipTrigger,\n} from \"@/components/ui/tooltip\"\n\nconst SIDEBAR_COOKIE_NAME = \"sidebar_state\"\nconst SIDEBAR_COOKIE_MAX_AGE = 60 * 60 * 24 * 7\nconst SIDEBAR_WIDTH = \"16rem\"\nconst SIDEBAR_WIDTH_MOBILE = \"18rem\"\nconst SIDEBAR_WIDTH_ICON = \"3rem\"\nconst SIDEBAR_KEYBOARD_SHORTCUT = \"b\"\n\ntype SidebarContext = {\n  state: \"expanded\" | \"collapsed\"\n  open: boolean\n  setOpen: (open: boolean) => void\n  openMobile: boolean\n  setOpenMobile: (open: boolean) => void\n  isMobile: boolean\n  toggleSidebar: () => void\n}\n\nconst SidebarContext = React.createContext<SidebarContext | null>(null)\n\nfunction useSidebar() {\n  const context = React.useContext(SidebarContext)\n  if (!context) {\n    throw new Error(\"useSidebar must be used within a SidebarProvider.\")\n  }\n\n  return context\n}\n\nconst SidebarProvider = React.forwardRef<\n  HTMLDivElement,\n  React.ComponentProps<\"div\"> & {\n    defaultOpen?: boolean\n    open?: boolean\n    onOpenChange?: (open: boolean) => void\n  }\n>(\n  (\n    {\n      defaultOpen = true,\n      open: openProp,\n      onOpenChange: setOpenProp,\n      className,\n      style,\n      children,\n      ...props\n    },\n    ref\n  ) => {\n    const isMobile = useIsMobile()\n    const [openMobile, setOpenMobile] = React.useState(false)\n\n    // This is the internal state of the sidebar.\n    // We use openProp and setOpenProp for control from outside the component.\n    const [_open, _setOpen] = React.useState(defaultOpen)\n    const open = openProp ?? _open\n    const setOpen = React.useCallback(\n      (value: boolean | ((value: boolean) => boolean)) => {\n        const openState = typeof value === \"function\" ? value(open) : value\n        if (setOpenProp) {\n          setOpenProp(openState)\n        } else {\n          _setOpen(openState)\n        }\n\n        // This sets the cookie to keep the sidebar state.\n        document.cookie = `${SIDEBAR_COOKIE_NAME}=${openState}; path=/; max-age=${SIDEBAR_COOKIE_MAX_AGE}`\n      },\n      [setOpenProp, open]\n    )\n\n    // Helper to toggle the sidebar.\n    const toggleSidebar = React.useCallback(() => {\n      return isMobile\n        ? setOpenMobile((open) => !open)\n        : setOpen((open) => !open)\n    }, [isMobile, setOpen, setOpenMobile])\n\n    // Adds a keyboard shortcut to toggle the sidebar.\n    React.useEffect(() => {\n      const handleKeyDown = (event: KeyboardEvent) => {\n        if (\n          event.key === SIDEBAR_KEYBOARD_SHORTCUT &&\n          (event.metaKey || event.ctrlKey)\n        ) {\n          event.preventDefault()\n          toggleSidebar()\n        }\n      }\n\n      window.addEventListener(\"keydown\", handleKeyDown)\n      return () => window.removeEventListener(\"keydown\", handleKeyDown)\n    }, [toggleSidebar])\n\n    // We add a state so that we can do data-state=\"expanded\" or \"collapsed\".\n    // This makes it easier to style the sidebar with Tailwind classes.\n    const state = open ? \"expanded\" : \"collapsed\"\n\n    const contextValue = React.useMemo<SidebarContext>(\n      () => ({\n        state,\n        open,\n        setOpen,\n        isMobile,\n        openMobile,\n        setOpenMobile,\n        toggleSidebar,\n      }),\n      [state, open, setOpen, isMobile, openMobile, setOpenMobile, toggleSidebar]\n    )\n\n    return (\n      <SidebarContext.Provider value={contextValue}>\n        <TooltipProvider delayDuration={0}>\n          <div\n            style={\n              {\n                \"--sidebar-width\": SIDEBAR_WIDTH,\n                \"--sidebar-width-icon\": SIDEBAR_WIDTH_ICON,\n                ...style,\n              } as React.CSSProperties\n            }\n            className={cn(\n              \"group/sidebar-wrapper flex min-h-svh w-full has-[[data-variant=inset]]:bg-sidebar\",\n              className\n            )}\n            ref={ref}\n            {...props}\n          >\n            {children}\n          </div>\n        </TooltipProvider>\n      </SidebarContext.Provider>\n    )\n  }\n)\nSidebarProvider.displayName = \"SidebarProvider\"\n\nconst Sidebar = React.forwardRef<\n  HTMLDivElement,\n  React.ComponentProps<\"div\"> & {\n    side?: \"left\" | \"right\"\n    variant?: \"sidebar\" | \"floating\" | \"inset\"\n    collapsible?: \"offcanvas\" | \"icon\" | \"none\"\n  }\n>(\n  (\n    {\n      side = \"left\",\n      variant = \"sidebar\",\n      collapsible = \"icon\",\n      className,\n      children,\n      ...props\n    },\n    ref\n  ) => {\n    const { isMobile, state, openMobile, setOpenMobile, toggleSidebar } = useSidebar()\n\n    if (collapsible === \"none\") {\n      return (\n        <div\n          className={cn(\n            \"flex h-full w-[--sidebar-width] flex-col bg-sidebar text-sidebar-foreground\",\n            className\n          )}\n          ref={ref}\n          {...props}\n        >\n          {children}\n        </div>\n      )\n    }\n\n    if (isMobile) {\n      return (\n        <Sheet open={openMobile} onOpenChange={setOpenMobile} {...props}>\n          <SheetContent\n            data-sidebar=\"sidebar\"\n            data-mobile=\"true\"\n            className=\"w-[--sidebar-width] bg-sidebar p-0 text-sidebar-foreground [&>button]:hidden\"\n            style={\n              {\n                \"--sidebar-width\": SIDEBAR_WIDTH_MOBILE,\n              } as React.CSSProperties\n            }\n            side={side}\n          >\n            <div className=\"flex h-full w-full flex-col\">{children}</div>\n          </SheetContent>\n        </Sheet>\n      )\n    }\n\n    return (\n      <div\n        ref={ref}\n        className=\"group peer hidden md:block text-sidebar-foreground\"\n        data-state={state}\n        data-collapsible={state === \"collapsed\" ? collapsible : \"\"}\n        data-variant={variant}\n        data-side={side}\n      >\n        {/* This is what handles the sidebar gap on desktop */}\n        <div\n          className={cn(\n            \"duration-200 relative h-svh w-[--sidebar-width] bg-transparent transition-[width] ease-linear\",\n            \"group-data-[collapsible=offcanvas]:w-0\",\n            \"group-data-[side=right]:rotate-180\",\n            variant === \"floating\" || variant === \"inset\"\n              ? \"group-data-[collapsible=icon]:w-[calc(var(--sidebar-width-icon)_+_theme(spacing.4))]\"\n              : \"group-data-[collapsible=icon]:w-[--sidebar-width-icon]\"\n          )}\n        />\n        <div\n          className={cn(\n            \"duration-200 fixed inset-y-0 z-10 hidden h-svh w-[--sidebar-width] transition-[left,right,width] ease-linear md:flex\",\n            side === \"left\"\n              ? \"left-0 group-data-[collapsible=offcanvas]:left-[calc(var(--sidebar-width)*-1)]\"\n              : \"right-0 group-data-[collapsible=offcanvas]:right-[calc(var(--sidebar-width)*-1)]\",\n            // Adjust the padding for floating and inset variants.\n            variant === \"floating\" || variant === \"inset\"\n              ? \"p-2 group-data-[collapsible=icon]:w-[calc(var(--sidebar-width-icon)_+_theme(spacing.4)_+2px)]\"\n              : \"group-data-[collapsible=icon]:w-[--sidebar-width-icon] group-data-[side=left]:border-r group-data-[side=right]:border-l\",\n            className\n          )}\n          {...props}\n        >\n          <div\n            data-sidebar=\"sidebar\"\n            className=\"flex h-full w-full flex-col bg-sidebar group-data-[variant=floating]:rounded-lg group-data-[variant=floating]:border group-data-[variant=floating]:border-sidebar-border group-data-[variant=floating]:shadow\"\n            onDoubleClick={toggleSidebar}\n          >\n            {children}\n          </div>\n        </div>\n      </div>\n    )\n  }\n)\nSidebar.displayName = \"Sidebar\"\n\nconst SidebarTrigger = React.forwardRef<\n  React.ElementRef<typeof Button>,\n  React.ComponentProps<typeof Button>\n>(({ className, onClick, ...props }, ref) => {\n  const { toggleSidebar } = useSidebar()\n\n  return (\n    <Button\n      ref={ref}\n      data-sidebar=\"trigger\"\n      variant=\"ghost\"\n      size=\"icon\"\n      className={cn(\"h-7 w-7\", className)}\n      onClick={(event) => {\n        onClick?.(event)\n        toggleSidebar()\n      }}\n      {...props}\n    >\n      <Menu />\n      <span className=\"sr-only\">Toggle Sidebar</span>\n    </Button>\n  )\n})\nSidebarTrigger.displayName = \"SidebarTrigger\"\n\nconst SidebarCollapse = React.forwardRef<\n  React.ElementRef<typeof Button>,\n  React.ComponentProps<typeof Button>\n>(({ className, onClick, ...props }, ref) => {\n  const { toggleSidebar, state } = useSidebar();\n  return (\n    <Button\n      ref={ref}\n      data-sidebar=\"collapse\"\n      variant=\"ghost\"\n      size=\"icon\"\n      className={cn(\"h-7 w-7\", className)}\n      onClick={(event) => {\n        onClick?.(event);\n        toggleSidebar();\n      }}\n      {...props}\n    >\n      {state === 'expanded' ? <ChevronsLeft /> : <ChevronsRight />}\n      <span className=\"sr-only\">Toggle Sidebar</span>\n    </Button>\n  );\n});\nSidebarCollapse.displayName = \"SidebarCollapse\";\n\n\nconst SidebarRail = React.forwardRef<\n  HTMLButtonElement,\n  React.ComponentProps<\"button\">\n>(({ className, ...props }, ref) => {\n  const { toggleSidebar } = useSidebar()\n\n  return (\n    <button\n      ref={ref}\n      data-sidebar=\"rail\"\n      aria-label=\"Toggle Sidebar\"\n      tabIndex={-1}\n      onClick={toggleSidebar}\n      title=\"Toggle Sidebar\"\n      className={cn(\n        \"absolute inset-y-0 z-20 hidden w-4 -translate-x-1/2 transition-all ease-linear after:absolute after:inset-y-0 after:left-1/2 after:w-[2px] hover:after:bg-sidebar-border group-data-[side=left]:-right-4 group-data-[side=right]:left-0 sm:flex\",\n        \"[[data-side=left]_&]:cursor-w-resize [[data-side=right]_&]:cursor-e-resize\",\n        \"[[data-side=left][data-state=collapsed]_&]:cursor-e-resize [[data-side=right][data-state=collapsed]_&]:cursor-w-resize\",\n        \"group-data-[collapsible=offcanvas]:translate-x-0 group-data-[collapsible=offcanvas]:after:left-full group-data-[collapsible=offcanvas]:hover:bg-sidebar\",\n        \"[[data-side=left][data-collapsible=offcanvas]_&]:-right-2\",\n        \"[[data-side=right][data-collapsible=offcanvas]_&]:-left-2\",\n        className\n      )}\n      {...props}\n    />\n  )\n})\nSidebarRail.displayName = \"SidebarRail\"\n\nconst SidebarInset = React.forwardRef<\n  HTMLDivElement,\n  React.ComponentProps<\"main\">\n>(({ className, ...props }, ref) => {\n  return (\n    <main\n      ref={ref}\n      className={cn(\n        \"relative flex min-h-svh flex-1 flex-col bg-background\",\n        \"peer-data-[variant=inset]:min-h-[calc(100svh-theme(spacing.4))] md:peer-data-[variant=inset]:m-2 md:peer-data-[state=collapsed]:peer-data-[variant=inset]:ml-2 md:peer-data-[variant=inset]:ml-0 md:peer-data-[variant=inset]:rounded-xl md:peer-data-[variant=inset]:shadow\",\n        className\n      )}\n      {...props}\n    />\n  )\n})\nSidebarInset.displayName = \"SidebarInset\"\n\nconst SidebarInput = React.forwardRef<\n  React.ElementRef<typeof Input>,\n  React.ComponentProps<typeof Input>\n>(({ className, ...props }, ref) => {\n  return (\n    <Input\n      ref={ref}\n      data-sidebar=\"input\"\n      className={cn(\n        \"h-8 w-full bg-background shadow-none focus-visible:ring-2 focus-visible:ring-sidebar-ring\",\n        className\n      )}\n      {...props}\n    />\n  )\n})\nSidebarInput.displayName = \"SidebarInput\"\n\nconst SidebarHeader = React.forwardRef<\n  HTMLDivElement,\n  React.ComponentProps<\"div\">\n>(({ className, ...props }, ref) => {\n  return (\n    <div\n      ref={ref}\n      data-sidebar=\"header\"\n      className={cn(\"flex items-center justify-between gap-2 p-2\", className)}\n      {...props}\n    />\n  )\n})\nSidebarHeader.displayName = \"SidebarHeader\"\n\nconst SidebarFooter = React.forwardRef<\n  HTMLDivElement,\n  React.ComponentProps<\"div\">\n>(({ className, ...props }, ref) => {\n  return (\n    <div\n      ref={ref}\n      data-sidebar=\"footer\"\n      className={cn(\"mt-auto flex flex-col\", className)}\n      {...props}\n    />\n  )\n})\nSidebarFooter.displayName = \"SidebarFooter\"\n\nconst SidebarSeparator = React.forwardRef<\n  React.ElementRef<typeof Separator>,\n  React.ComponentProps<typeof Separator>\n>(({ className, ...props }, ref) => {\n  return (\n    <Separator\n      ref={ref}\n      data-sidebar=\"separator\"\n      className={cn(\"mx-2 w-auto bg-sidebar-border\", className)}\n      {...props}\n    />\n  )\n})\nSidebarSeparator.displayName = \"SidebarSeparator\"\n\nconst SidebarContent = React.forwardRef<\n  HTMLDivElement,\n  React.ComponentProps<\"div\">\n>(({ className, ...props }, ref) => {\n  return (\n    <div\n      ref={ref}\n      data-sidebar=\"content\"\n      className={cn(\n        \"flex min-h-0 flex-1 flex-col gap-2 overflow-auto group-data-[collapsible=icon]:overflow-hidden\",\n        className\n      )}\n      {...props}\n    />\n  )\n})\nSidebarContent.displayName = \"SidebarContent\"\n\nconst SidebarGroup = React.forwardRef<\n  HTMLDivElement,\n  React.ComponentProps<\"div\">\n>(({ className, ...props }, ref) => {\n  return (\n    <div\n      ref={ref}\n      data-sidebar=\"group\"\n      className={cn(\"relative flex w-full min-w-0 flex-col p-2\", className)}\n      {...props}\n    />\n  )\n})\nSidebarGroup.displayName = \"SidebarGroup\"\n\nconst SidebarGroupLabel = React.forwardRef<\n  HTMLDivElement,\n  React.ComponentProps<\"div\"> & { asChild?: boolean }\n>(({ className, asChild = false, ...props }, ref) => {\n  const Comp = asChild ? Slot : \"div\"\n\n  return (\n    <Comp\n      ref={ref}\n      data-sidebar=\"group-label\"\n      className={cn(\n        \"duration-200 flex h-8 shrink-0 items-center rounded-md px-2 text-xs font-medium text-sidebar-foreground/70 outline-none ring-sidebar-ring transition-[margin,opa] ease-linear focus-visible:ring-2 [&>svg]:size-4 [&>svg]:shrink-0\",\n        \"group-data-[collapsible=icon]:-mt-8 group-data-[collapsible=icon]:opacity-0\",\n        className\n      )}\n      {...props}\n    />\n  )\n})\nSidebarGroupLabel.displayName = \"SidebarGroupLabel\"\n\nconst SidebarGroupAction = React.forwardRef<\n  HTMLButtonElement,\n  React.ComponentProps<\"button\"> & { asChild?: boolean }\n>(({ className, asChild = false, ...props }, ref) => {\n  const Comp = asChild ? Slot : \"button\"\n\n  return (\n    <Comp\n      ref={ref}\n      data-sidebar=\"group-action\"\n      className={cn(\n        \"absolute right-3 top-3.5 flex aspect-square w-5 items-center justify-center rounded-md p-0 text-sidebar-foreground outline-none ring-sidebar-ring transition-transform hover:bg-sidebar-accent hover:text-sidebar-accent-foreground focus-visible:ring-2 [&>svg]:size-4 [&>svg]:shrink-0\",\n        // Increases the hit area of the button on mobile.\n        \"after:absolute after:-inset-2 after:md:hidden\",\n        \"group-data-[collapsible=icon]:hidden\",\n        className\n      )}\n      {...props}\n    />\n  )\n})\nSidebarGroupAction.displayName = \"SidebarGroupAction\"\n\nconst SidebarGroupContent = React.forwardRef<\n  HTMLDivElement,\n  React.ComponentProps<\"div\">\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    data-sidebar=\"group-content\"\n    className={cn(\"w-full text-sm\", className)}\n    {...props}\n  />\n))\nSidebarGroupContent.displayName = \"SidebarGroupContent\"\n\nconst SidebarMenu = React.forwardRef<\n  HTMLUListElement,\n  React.ComponentProps<\"ul\">\n>(({ className, ...props }, ref) => (\n  <ul\n    ref={ref}\n    data-sidebar=\"menu\"\n    className={cn(\"flex w-full min-w-0 flex-col gap-1\", className)}\n    {...props}\n  />\n))\nSidebarMenu.displayName = \"SidebarMenu\"\n\nconst SidebarMenuItem = React.forwardRef<\n  HTMLLIElement,\n  React.ComponentProps<\"li\">\n>(({ className, ...props }, ref) => (\n  <li\n    ref={ref}\n    data-sidebar=\"menu-item\"\n    className={cn(\"group/menu-item relative\", className)}\n    {...props}\n  />\n))\nSidebarMenuItem.displayName = \"SidebarMenuItem\"\n\nconst sidebarMenuButtonVariants = cva(\n  \"peer/menu-button flex w-full items-center gap-2 overflow-hidden rounded-md p-2 text-left text-sm outline-none ring-sidebar-ring transition-[width,height,padding] hover:bg-sidebar-accent hover:text-sidebar-accent-foreground focus-visible:ring-2 active:bg-sidebar-accent active:text-sidebar-accent-foreground disabled:pointer-events-none disabled:opacity-50 group-has-[[data-sidebar=menu-action]]/menu-item:pr-8 aria-disabled:pointer-events-none aria-disabled:opacity-50 data-[active=true]:bg-sidebar-accent data-[active=true]:font-medium data-[active=true]:text-sidebar-accent-foreground data-[state=open]:hover:bg-sidebar-accent data-[state=open]:hover:text-sidebar-accent-foreground group-data-[collapsible=icon]:!size-8 group-data-[collapsible=icon]:!p-2 [&>span:last-child]:truncate [&>svg]:size-4 [&>svg]:shrink-0\",\n  {\n    variants: {\n      variant: {\n        default: \"hover:bg-sidebar-accent hover:text-sidebar-accent-foreground\",\n        outline:\n          \"bg-background shadow-[0_0_0_1px_hsl(var(--sidebar-border))] hover:bg-sidebar-accent hover:text-sidebar-accent-foreground hover:shadow-[0_0_0_1px_hsl(var(--sidebar-accent))]\",\n      },\n      size: {\n        default: \"h-8 text-sm\",\n        sm: \"h-7 text-xs\",\n        lg: \"h-12 text-sm group-data-[collapsible=icon]:!p-0\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nconst SidebarMenuButton = React.forwardRef<\n  HTMLButtonElement,\n  React.ComponentProps<\"button\"> & {\n    asChild?: boolean\n    isActive?: boolean\n    tooltip?: string | React.ComponentProps<typeof TooltipContent>\n  } & VariantProps<typeof sidebarMenuButtonVariants>\n>(\n  (\n    {\n      asChild = false,\n      isActive = false,\n      variant = \"default\",\n      size = \"default\",\n      tooltip,\n      className,\n      onClick,\n      ...props\n    },\n    ref\n  ) => {\n    const Comp = asChild ? Slot : \"button\"\n    const { isMobile, state } = useSidebar()\n\n    const button = (\n      <Comp\n        ref={ref}\n        data-sidebar=\"menu-button\"\n        data-size={size}\n        data-active={isActive}\n        className={cn(sidebarMenuButtonVariants({ variant, size }), className)}\n        onClick={(e) => {\n          e.preventDefault();\n          onClick?.(e);\n        }}\n        {...props}\n      />\n    )\n\n    if (!tooltip) {\n      return button\n    }\n\n    if (typeof tooltip === \"string\") {\n      tooltip = {\n        children: tooltip,\n      }\n    }\n\n    return (\n      <Tooltip>\n        <TooltipTrigger asChild>{button}</TooltipTrigger>\n        <TooltipContent\n          side=\"right\"\n          align=\"center\"\n          hidden={state !== \"collapsed\" || isMobile}\n          {...tooltip}\n        />\n      </Tooltip>\n    )\n  }\n)\nSidebarMenuButton.displayName = \"SidebarMenuButton\"\n\nconst SidebarMenuAction = React.forwardRef<\n  HTMLButtonElement,\n  React.ComponentProps<\"button\"> & {\n    asChild?: boolean\n    showOnHover?: boolean\n  }\n>(({ className, asChild = false, showOnHover = false, ...props }, ref) => {\n  const Comp = asChild ? Slot : \"button\"\n\n  return (\n    <Comp\n      ref={ref}\n      data-sidebar=\"menu-action\"\n      className={cn(\n        \"absolute right-1 top-1.5 flex aspect-square w-5 items-center justify-center rounded-md p-0 text-sidebar-foreground outline-none ring-sidebar-ring transition-transform hover:bg-sidebar-accent hover:text-sidebar-accent-foreground focus-visible:ring-2 peer-hover/menu-button:text-sidebar-accent-foreground [&>svg]:size-4 [&>svg]:shrink-0\",\n        // Increases the hit area of the button on mobile.\n        \"after:absolute after:-inset-2 after:md:hidden\",\n        \"peer-data-[size=sm]/menu-button:top-1\",\n        \"peer-data-[size=default]/menu-button:top-1.5\",\n        \"peer-data-[size=lg]/menu-button:top-2.5\",\n        \"group-data-[collapsible=icon]:hidden\",\n        showOnHover &&\n          \"group-focus-within/menu-item:opacity-100 group-hover/menu-item:opacity-100 data-[state=open]:opacity-100 peer-data-[active=true]/menu-button:text-sidebar-accent-foreground md:opacity-0\",\n        className\n      )}\n      {...props}\n    />\n  )\n})\nSidebarMenuAction.displayName = \"SidebarMenuAction\"\n\nconst SidebarMenuBadge = React.forwardRef<\n  HTMLDivElement,\n  React.ComponentProps<\"div\">\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    data-sidebar=\"menu-badge\"\n    className={cn(\n      \"absolute right-1 flex h-5 min-w-5 items-center justify-center rounded-md px-1 text-xs font-medium tabular-nums text-sidebar-foreground select-none pointer-events-none\",\n      \"peer-hover/menu-button:text-sidebar-accent-foreground peer-data-[active=true]/menu-button:text-sidebar-accent-foreground\",\n      \"peer-data-[size=sm]/menu-button:top-1\",\n      \"peer-data-[size=default]/menu-button:top-1.5\",\n      \"peer-data-[size=lg]/menu-button:top-2.5\",\n      \"group-data-[collapsible=icon]:hidden\",\n      className\n    )}\n    {...props}\n  />\n))\nSidebarMenuBadge.displayName = \"SidebarMenuBadge\"\n\nconst SidebarMenuSkeleton = React.forwardRef<\n  HTMLDivElement,\n  React.ComponentProps<\"div\"> & {\n    showIcon?: boolean\n  }\n>(({ className, showIcon = false, ...props }, ref) => {\n  // Random width between 50 to 90%.\n  const width = React.useMemo(() => {\n    return `${Math.floor(Math.random() * 40) + 50}%`\n  }, [])\n\n  return (\n    <div\n      ref={ref}\n      data-sidebar=\"menu-skeleton\"\n      className={cn(\"rounded-md h-8 flex gap-2 px-2 items-center\", className)}\n      {...props}\n    >\n      {showIcon && (\n        <Skeleton\n          className=\"size-4 rounded-md\"\n          data-sidebar=\"menu-skeleton-icon\"\n        />\n      )}\n      <Skeleton\n        className=\"h-4 flex-1 max-w-[--skeleton-width]\"\n        data-sidebar=\"menu-skeleton-text\"\n        style={\n          {\n            \"--skeleton-width\": width,\n          } as React.CSSProperties\n        }\n      />\n    </div>\n  )\n})\nSidebarMenuSkeleton.displayName = \"SidebarMenuSkeleton\"\n\nconst SidebarMenuSub = React.forwardRef<\n  HTMLUListElement,\n  React.ComponentProps<\"ul\">\n>(({ className, ...props }, ref) => (\n  <ul\n    ref={ref}\n    data-sidebar=\"menu-sub\"\n    className={cn(\n      \"mx-3.5 flex min-w-0 translate-x-px flex-col gap-1 border-l border-sidebar-border px-2.5 py-0.5\",\n      \"group-data-[collapsible=icon]:hidden\",\n      className\n    )}\n    {...props}\n  />\n))\nSidebarMenuSub.displayName = \"SidebarMenuSub\"\n\nconst SidebarMenuSubItem = React.forwardRef<\n  HTMLLIElement,\n  React.ComponentProps<\"li\">\n>(({ ...props }, ref) => <li ref={ref} {...props} />)\nSidebarMenuSubItem.displayName = \"SidebarMenuSubItem\"\n\nconst SidebarMenuSubButton = React.forwardRef<\n  HTMLAnchorElement,\n  React.ComponentProps<\"a\"> & {\n    asChild?: boolean\n    size?: \"sm\" | \"md\"\n    isActive?: boolean\n  }\n>(({ asChild = false, size = \"md\", isActive, className, ...props }, ref) => {\n  const Comp = asChild ? Slot : \"a\"\n\n  return (\n    <Comp\n      ref={ref}\n      data-sidebar=\"menu-sub-button\"\n      data-size={size}\n      data-active={isActive}\n      className={cn(\n        \"flex h-7 min-w-0 -translate-x-px items-center gap-2 overflow-hidden rounded-md px-2 text-sidebar-foreground outline-none ring-sidebar-ring hover:bg-sidebar-accent hover:text-sidebar-accent-foreground focus-visible:ring-2 active:bg-sidebar-accent active:text-sidebar-accent-foreground disabled:pointer-events-none disabled:opacity-50 aria-disabled:pointer-events-none aria-disabled:opacity-50 [&>span:last-child]:truncate [&>svg]:size-4 [&>svg]:shrink-0 [&>svg]:text-sidebar-accent-foreground\",\n        \"data-[active=true]:bg-sidebar-accent data-[active=true]:text-sidebar-accent-foreground\",\n        size === \"sm\" && \"text-xs\",\n        size === \"md\" && \"text-sm\",\n        \"group-data-[collapsible=icon]:hidden\",\n        className\n      )}\n      {...props}\n    />\n  )\n})\nSidebarMenuSubButton.displayName = \"SidebarMenuSubButton\"\n\nexport {\n  Sidebar,\n  SidebarCollapse,\n  SidebarContent,\n  SidebarFooter,\n  SidebarGroup,\n  SidebarGroupAction,\n  SidebarGroupContent,\n  SidebarGroupLabel,\n  SidebarHeader,\n  SidebarInput,\n  SidebarInset,\n  SidebarMenu,\n  SidebarMenuAction,\n  SidebarMenuBadge,\n  SidebarMenuButton,\n  SidebarMenuItem,\n  SidebarMenuSkeleton,\n  SidebarMenuSub,\n  SidebarMenuSubButton,\n  SidebarMenuSubItem,\n  SidebarProvider,\n  SidebarRail,\n  SidebarSeparator,\n  SidebarTrigger,\n  useSidebar,\n}\n\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;AAGA;AACA;AACA;AACA;AAAA;AAAA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAdA;;;;;;;;;;;;;;AAqBA,MAAM,sBAAsB;AAC5B,MAAM,yBAAyB,KAAK,KAAK,KAAK;AAC9C,MAAM,gBAAgB;AACtB,MAAM,uBAAuB;AAC7B,MAAM,qBAAqB;AAC3B,MAAM,4BAA4B;AAYlC,MAAM,+BAAiB,CAAA,GAAA,qMAAA,CAAA,gBAAmB,AAAD,EAAyB;AAElE,SAAS;IACP,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAAE;IACjC,IAAI,CAAC,SAAS;QACZ,MAAM,IAAI,MAAM;IAClB;IAEA,OAAO;AACT;AAEA,MAAM,gCAAkB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAQrC,CACE,EACE,cAAc,IAAI,EAClB,MAAM,QAAQ,EACd,cAAc,WAAW,EACzB,SAAS,EACT,KAAK,EACL,QAAQ,EACR,GAAG,OACJ,EACD;IAEA,MAAM,WAAW,CAAA,GAAA,8HAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAc,AAAD,EAAE;IAEnD,6CAA6C;IAC7C,0EAA0E;IAC1E,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAc,AAAD,EAAE;IACzC,MAAM,OAAO,YAAY;IACzB,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,cAAiB,AAAD,EAC9B,CAAC;QACC,MAAM,YAAY,OAAO,UAAU,aAAa,MAAM,QAAQ;QAC9D,IAAI,aAAa;YACf,YAAY;QACd,OAAO;YACL,SAAS;QACX;QAEA,kDAAkD;QAClD,SAAS,MAAM,GAAG,GAAG,oBAAoB,CAAC,EAAE,UAAU,kBAAkB,EAAE,wBAAwB;IACpG,GACA;QAAC;QAAa;KAAK;IAGrB,gCAAgC;IAChC,MAAM,gBAAgB,CAAA,GAAA,qMAAA,CAAA,cAAiB,AAAD,EAAE;QACtC,OAAO,WACH,cAAc,CAAC,OAAS,CAAC,QACzB,QAAQ,CAAC,OAAS,CAAC;IACzB,GAAG;QAAC;QAAU;QAAS;KAAc;IAErC,kDAAkD;IAClD,CAAA,GAAA,qMAAA,CAAA,YAAe,AAAD,EAAE;QACd,MAAM,gBAAgB,CAAC;YACrB,IACE,MAAM,GAAG,KAAK,6BACd,CAAC,MAAM,OAAO,IAAI,MAAM,OAAO,GAC/B;gBACA,MAAM,cAAc;gBACpB;YACF;QACF;QAEA,OAAO,gBAAgB,CAAC,WAAW;QACnC,OAAO,IAAM,OAAO,mBAAmB,CAAC,WAAW;IACrD,GAAG;QAAC;KAAc;IAElB,yEAAyE;IACzE,mEAAmE;IACnE,MAAM,QAAQ,OAAO,aAAa;IAElC,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,UAAa,AAAD,EAC/B,IAAM,CAAC;YACL;YACA;YACA;YACA;YACA;YACA;YACA;QACF,CAAC,GACD;QAAC;QAAO;QAAM;QAAS;QAAU;QAAY;QAAe;KAAc;IAG5E,qBACE,8OAAC,eAAe,QAAQ;QAAC,OAAO;kBAC9B,cAAA,8OAAC,mIAAA,CAAA,kBAAe;YAAC,eAAe;sBAC9B,cAAA,8OAAC;gBACC,OACE;oBACE,mBAAmB;oBACnB,wBAAwB;oBACxB,GAAG,KAAK;gBACV;gBAEF,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,qFACA;gBAEF,KAAK;gBACJ,GAAG,KAAK;0BAER;;;;;;;;;;;;;;;;AAKX;AAEF,gBAAgB,WAAW,GAAG;AAE9B,MAAM,wBAAU,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAQ7B,CACE,EACE,OAAO,MAAM,EACb,UAAU,SAAS,EACnB,cAAc,MAAM,EACpB,SAAS,EACT,QAAQ,EACR,GAAG,OACJ,EACD;IAEA,MAAM,EAAE,QAAQ,EAAE,KAAK,EAAE,UAAU,EAAE,aAAa,EAAE,aAAa,EAAE,GAAG;IAEtE,IAAI,gBAAgB,QAAQ;QAC1B,qBACE,8OAAC;YACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,+EACA;YAEF,KAAK;YACJ,GAAG,KAAK;sBAER;;;;;;IAGP;IAEA,IAAI,UAAU;QACZ,qBACE,8OAAC,iIAAA,CAAA,QAAK;YAAC,MAAM;YAAY,cAAc;YAAgB,GAAG,KAAK;sBAC7D,cAAA,8OAAC,iIAAA,CAAA,eAAY;gBACX,gBAAa;gBACb,eAAY;gBACZ,WAAU;gBACV,OACE;oBACE,mBAAmB;gBACrB;gBAEF,MAAM;0BAEN,cAAA,8OAAC;oBAAI,WAAU;8BAA+B;;;;;;;;;;;;;;;;IAItD;IAEA,qBACE,8OAAC;QACC,KAAK;QACL,WAAU;QACV,cAAY;QACZ,oBAAkB,UAAU,cAAc,cAAc;QACxD,gBAAc;QACd,aAAW;;0BAGX,8OAAC;gBACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,iGACA,0CACA,sCACA,YAAY,cAAc,YAAY,UAClC,yFACA;;;;;;0BAGR,8OAAC;gBACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wHACA,SAAS,SACL,mFACA,oFACJ,sDAAsD;gBACtD,YAAY,cAAc,YAAY,UAClC,kGACA,2HACJ;gBAED,GAAG,KAAK;0BAET,cAAA,8OAAC;oBACC,gBAAa;oBACb,WAAU;oBACV,eAAe;8BAEd;;;;;;;;;;;;;;;;;AAKX;AAEF,QAAQ,WAAW,GAAG;AAEtB,MAAM,+BAAiB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGpC,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG,OAAO,EAAE;IACnC,MAAM,EAAE,aAAa,EAAE,GAAG;IAE1B,qBACE,8OAAC,kIAAA,CAAA,SAAM;QACL,KAAK;QACL,gBAAa;QACb,SAAQ;QACR,MAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,WAAW;QACzB,SAAS,CAAC;YACR,UAAU;YACV;QACF;QACC,GAAG,KAAK;;0BAET,8OAAC,kMAAA,CAAA,OAAI;;;;;0BACL,8OAAC;gBAAK,WAAU;0BAAU;;;;;;;;;;;;AAGhC;AACA,eAAe,WAAW,GAAG;AAE7B,MAAM,gCAAkB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGrC,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG,OAAO,EAAE;IACnC,MAAM,EAAE,aAAa,EAAE,KAAK,EAAE,GAAG;IACjC,qBACE,8OAAC,kIAAA,CAAA,SAAM;QACL,KAAK;QACL,gBAAa;QACb,SAAQ;QACR,MAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,WAAW;QACzB,SAAS,CAAC;YACR,UAAU;YACV;QACF;QACC,GAAG,KAAK;;YAER,UAAU,2BAAa,8OAAC,sNAAA,CAAA,eAAY;;;;qCAAM,8OAAC,wNAAA,CAAA,gBAAa;;;;;0BACzD,8OAAC;gBAAK,WAAU;0BAAU;;;;;;;;;;;;AAGhC;AACA,gBAAgB,WAAW,GAAG;AAG9B,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE;IAC1B,MAAM,EAAE,aAAa,EAAE,GAAG;IAE1B,qBACE,8OAAC;QACC,KAAK;QACL,gBAAa;QACb,cAAW;QACX,UAAU,CAAC;QACX,SAAS;QACT,OAAM;QACN,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,mPACA,8EACA,0HACA,2JACA,6DACA,6DACA;QAED,GAAG,KAAK;;;;;;AAGf;AACA,YAAY,WAAW,GAAG;AAE1B,MAAM,6BAAe,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGlC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE;IAC1B,qBACE,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,yDACA,gRACA;QAED,GAAG,KAAK;;;;;;AAGf;AACA,aAAa,WAAW,GAAG;AAE3B,MAAM,6BAAe,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGlC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE;IAC1B,qBACE,8OAAC,iIAAA,CAAA,QAAK;QACJ,KAAK;QACL,gBAAa;QACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,6FACA;QAED,GAAG,KAAK;;;;;;AAGf;AACA,aAAa,WAAW,GAAG;AAE3B,MAAM,8BAAgB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGnC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE;IAC1B,qBACE,8OAAC;QACC,KAAK;QACL,gBAAa;QACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,+CAA+C;QAC5D,GAAG,KAAK;;;;;;AAGf;AACA,cAAc,WAAW,GAAG;AAE5B,MAAM,8BAAgB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGnC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE;IAC1B,qBACE,8OAAC;QACC,KAAK;QACL,gBAAa;QACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,yBAAyB;QACtC,GAAG,KAAK;;;;;;AAGf;AACA,cAAc,WAAW,GAAG;AAE5B,MAAM,iCAAmB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGtC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE;IAC1B,qBACE,8OAAC,qIAAA,CAAA,YAAS;QACR,KAAK;QACL,gBAAa;QACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;AACA,iBAAiB,WAAW,GAAG;AAE/B,MAAM,+BAAiB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGpC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE;IAC1B,qBACE,8OAAC;QACC,KAAK;QACL,gBAAa;QACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kGACA;QAED,GAAG,KAAK;;;;;;AAGf;AACA,eAAe,WAAW,GAAG;AAE7B,MAAM,6BAAe,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGlC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE;IAC1B,qBACE,8OAAC;QACC,KAAK;QACL,gBAAa;QACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,6CAA6C;QAC1D,GAAG,KAAK;;;;;;AAGf;AACA,aAAa,WAAW,GAAG;AAE3B,MAAM,kCAAoB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGvC,CAAC,EAAE,SAAS,EAAE,UAAU,KAAK,EAAE,GAAG,OAAO,EAAE;IAC3C,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,KAAK;QACL,gBAAa;QACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,sOACA,+EACA;QAED,GAAG,KAAK;;;;;;AAGf;AACA,kBAAkB,WAAW,GAAG;AAEhC,MAAM,mCAAqB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGxC,CAAC,EAAE,SAAS,EAAE,UAAU,KAAK,EAAE,GAAG,OAAO,EAAE;IAC3C,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,KAAK;QACL,gBAAa;QACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,4RACA,kDAAkD;QAClD,iDACA,wCACA;QAED,GAAG,KAAK;;;;;;AAGf;AACA,mBAAmB,WAAW,GAAG;AAEjC,MAAM,oCAAsB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGzC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,gBAAa;QACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,kBAAkB;QAC/B,GAAG,KAAK;;;;;;AAGb,oBAAoB,WAAW,GAAG;AAElC,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,gBAAa;QACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;;;;;;AAGb,YAAY,WAAW,GAAG;AAE1B,MAAM,gCAAkB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,gBAAa;QACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,4BAA4B;QACzC,GAAG,KAAK;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAA4B,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EAClC,qzBACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,SACE;QACJ;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;QACN;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,MAAM,kCAAoB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAQvC,CACE,EACE,UAAU,KAAK,EACf,WAAW,KAAK,EAChB,UAAU,SAAS,EACnB,OAAO,SAAS,EAChB,OAAO,EACP,SAAS,EACT,OAAO,EACP,GAAG,OACJ,EACD;IAEA,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAC9B,MAAM,EAAE,QAAQ,EAAE,KAAK,EAAE,GAAG;IAE5B,MAAM,uBACJ,8OAAC;QACC,KAAK;QACL,gBAAa;QACb,aAAW;QACX,eAAa;QACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,0BAA0B;YAAE;YAAS;QAAK,IAAI;QAC5D,SAAS,CAAC;YACR,EAAE,cAAc;YAChB,UAAU;QACZ;QACC,GAAG,KAAK;;;;;;IAIb,IAAI,CAAC,SAAS;QACZ,OAAO;IACT;IAEA,IAAI,OAAO,YAAY,UAAU;QAC/B,UAAU;YACR,UAAU;QACZ;IACF;IAEA,qBACE,8OAAC,mIAAA,CAAA,UAAO;;0BACN,8OAAC,mIAAA,CAAA,iBAAc;gBAAC,OAAO;0BAAE;;;;;;0BACzB,8OAAC,mIAAA,CAAA,iBAAc;gBACb,MAAK;gBACL,OAAM;gBACN,QAAQ,UAAU,eAAe;gBAChC,GAAG,OAAO;;;;;;;;;;;;AAInB;AAEF,kBAAkB,WAAW,GAAG;AAEhC,MAAM,kCAAoB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAMvC,CAAC,EAAE,SAAS,EAAE,UAAU,KAAK,EAAE,cAAc,KAAK,EAAE,GAAG,OAAO,EAAE;IAChE,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,KAAK;QACL,gBAAa;QACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kVACA,kDAAkD;QAClD,iDACA,yCACA,gDACA,2CACA,wCACA,eACE,4LACF;QAED,GAAG,KAAK;;;;;;AAGf;AACA,kBAAkB,WAAW,GAAG;AAEhC,MAAM,iCAAmB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGtC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,gBAAa;QACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0KACA,4HACA,yCACA,gDACA,2CACA,wCACA;QAED,GAAG,KAAK;;;;;;AAGb,iBAAiB,WAAW,GAAG;AAE/B,MAAM,oCAAsB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAKzC,CAAC,EAAE,SAAS,EAAE,WAAW,KAAK,EAAE,GAAG,OAAO,EAAE;IAC5C,kCAAkC;IAClC,MAAM,QAAQ,CAAA,GAAA,qMAAA,CAAA,UAAa,AAAD,EAAE;QAC1B,OAAO,GAAG,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,MAAM,GAAG,CAAC,CAAC;IAClD,GAAG,EAAE;IAEL,qBACE,8OAAC;QACC,KAAK;QACL,gBAAa;QACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,+CAA+C;QAC5D,GAAG,KAAK;;YAER,0BACC,8OAAC,oIAAA,CAAA,WAAQ;gBACP,WAAU;gBACV,gBAAa;;;;;;0BAGjB,8OAAC,oIAAA,CAAA,WAAQ;gBACP,WAAU;gBACV,gBAAa;gBACb,OACE;oBACE,oBAAoB;gBACtB;;;;;;;;;;;;AAKV;AACA,oBAAoB,WAAW,GAAG;AAElC,MAAM,+BAAiB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGpC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,gBAAa;QACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kGACA,wCACA;QAED,GAAG,KAAK;;;;;;AAGb,eAAe,WAAW,GAAG;AAE7B,MAAM,mCAAqB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGxC,CAAC,EAAE,GAAG,OAAO,EAAE,oBAAQ,8OAAC;QAAG,KAAK;QAAM,GAAG,KAAK;;;;;;AAChD,mBAAmB,WAAW,GAAG;AAEjC,MAAM,qCAAuB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAO1C,CAAC,EAAE,UAAU,KAAK,EAAE,OAAO,IAAI,EAAE,QAAQ,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE;IAClE,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,KAAK;QACL,gBAAa;QACb,aAAW;QACX,eAAa;QACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,+eACA,0FACA,SAAS,QAAQ,WACjB,SAAS,QAAQ,WACjB,wCACA;QAED,GAAG,KAAK;;;;;;AAGf;AACA,qBAAqB,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 1037, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/src/components/icons/custom-logo.tsx"], "sourcesContent": ["\nimport type { SVGProps } from 'react';\n\nexport function CustomLogo(props: SVGProps<SVGSVGElement>) {\n  return (\n    <svg\n      width=\"100\"\n      height=\"100\"\n      viewBox=\"0 0 100 100\"\n      fill=\"none\"\n      xmlns=\"http://www.w3.org/2000/svg\"\n      {...props}\n    >\n      <defs>\n        <linearGradient\n          id=\"logoGradient\"\n          x1=\"0\"\n          y1=\"0\"\n          x2=\"100\"\n          y2=\"100\"\n          gradientUnits=\"userSpaceOnUse\"\n        >\n          <stop stopColor=\"#6366F1\" />\n          <stop offset=\"1\" stopColor=\"#8B5CF6\" />\n        </linearGradient>\n      </defs>\n      <circle cx=\"50\" cy=\"50\" r=\"48\" fill=\"url(#logoGradient)\" />\n      <text\n        x=\"50\"\n        y=\"45\"\n        fontFamily=\"'Cormorant SC', serif\"\n        fontSize=\"36\"\n        fontWeight=\"500\"\n        fill=\"white\"\n        textAnchor=\"middle\"\n        dominantBaseline=\"central\"\n      >\n        LF\n      </text>\n      <g transform=\"translate(0, 5)\">\n        <path\n            d=\"M30 65 L42 75 L65 50\"\n            stroke=\"white\"\n            strokeWidth=\"5\"\n            strokeLinecap=\"round\"\n            strokeLinejoin=\"round\"\n            fill=\"none\"\n          />\n        <path\n            d=\"M45 65 L57 75 L80 50\"\n            stroke=\"white\"\n            strokeWidth=\"5\"\n            strokeLinecap=\"round\"\n            strokeLinejoin=\"round\"\n            fill=\"none\"\n            strokeOpacity=\"0.6\"\n        />\n      </g>\n    </svg>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAGO,SAAS,WAAW,KAA8B;IACvD,qBACE,8OAAC;QACC,OAAM;QACN,QAAO;QACP,SAAQ;QACR,MAAK;QACL,OAAM;QACL,GAAG,KAAK;;0BAET,8OAAC;0BACC,cAAA,8OAAC;oBACC,IAAG;oBACH,IAAG;oBACH,IAAG;oBACH,IAAG;oBACH,IAAG;oBACH,eAAc;;sCAEd,8OAAC;4BAAK,WAAU;;;;;;sCAChB,8OAAC;4BAAK,QAAO;4BAAI,WAAU;;;;;;;;;;;;;;;;;0BAG/B,8OAAC;gBAAO,IAAG;gBAAK,IAAG;gBAAK,GAAE;gBAAK,MAAK;;;;;;0BACpC,8OAAC;gBACC,GAAE;gBACF,GAAE;gBACF,YAAW;gBACX,UAAS;gBACT,YAAW;gBACX,MAAK;gBACL,YAAW;gBACX,kBAAiB;0BAClB;;;;;;0BAGD,8OAAC;gBAAE,WAAU;;kCACX,8OAAC;wBACG,GAAE;wBACF,QAAO;wBACP,aAAY;wBACZ,eAAc;wBACd,gBAAe;wBACf,MAAK;;;;;;kCAET,8OAAC;wBACG,GAAE;wBACF,QAAO;wBACP,aAAY;wBACZ,eAAc;wBACd,gBAAe;wBACf,MAAK;wBACL,eAAc;;;;;;;;;;;;;;;;;;AAK1B", "debugId": null}}, {"offset": {"line": 1158, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/src/components/ui/label.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as LabelPrimitive from \"@radix-ui/react-label\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst labelVariants = cva(\n  \"text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70\"\n)\n\nconst Label = React.forwardRef<\n  React.ElementRef<typeof LabelPrimitive.Root>,\n  React.ComponentPropsWithoutRef<typeof LabelPrimitive.Root> &\n    VariantProps<typeof labelVariants>\n>(({ className, ...props }, ref) => (\n  <LabelPrimitive.Root\n    ref={ref}\n    className={cn(labelVariants(), className)}\n    {...props}\n  />\n))\nLabel.displayName = LabelPrimitive.Root.displayName\n\nexport { Label }\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAEA;AANA;;;;;;AAQA,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACtB;AAGF,MAAM,sBAAQ,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAI3B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,iKAAA,CAAA,OAAmB;QAClB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iBAAiB;QAC9B,GAAG,KAAK;;;;;;AAGb,MAAM,WAAW,GAAG,iKAAA,CAAA,OAAmB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 1190, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/src/components/ui/select.tsx"], "sourcesContent": ["\n\"use client\"\n\nimport * as React from \"react\"\nimport * as SelectPrimitive from \"@radix-ui/react-select\"\nimport { Check, ChevronDown, ChevronUp } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Select = SelectPrimitive.Root\n\nconst SelectGroup = SelectPrimitive.Group\n\nconst SelectValue = SelectPrimitive.Value\n\nconst SelectTrigger = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Trigger>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Trigger>\n>(({ className, children, ...props }, ref) => (\n  <SelectPrimitive.Trigger\n    ref={ref}\n    className={cn(\n      \"flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1\",\n      className\n    )}\n    {...props}\n  >\n    {children}\n    <SelectPrimitive.Icon asChild>\n      <ChevronDown className=\"h-4 w-4 opacity-50\" />\n    </SelectPrimitive.Icon>\n  </SelectPrimitive.Trigger>\n))\nSelectTrigger.displayName = SelectPrimitive.Trigger.displayName\n\nconst SelectScrollUpButton = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.ScrollUpButton>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.ScrollUpButton>\n>(({ className, ...props }, ref) => (\n  <SelectPrimitive.ScrollUpButton\n    ref={ref}\n    className={cn(\n      \"flex cursor-default items-center justify-center py-1\",\n      className\n    )}\n    {...props}\n  >\n    <ChevronUp className=\"h-4 w-4\" />\n  </SelectPrimitive.ScrollUpButton>\n))\nSelectScrollUpButton.displayName = SelectPrimitive.ScrollUpButton.displayName\n\nconst SelectScrollDownButton = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.ScrollDownButton>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.ScrollDownButton>\n>(({ className, ...props }, ref) => (\n  <SelectPrimitive.ScrollDownButton\n    ref={ref}\n    className={cn(\n      \"flex cursor-default items-center justify-center py-1\",\n      className\n    )}\n    {...props}\n  >\n    <ChevronDown className=\"h-4 w-4\" />\n  </SelectPrimitive.ScrollDownButton>\n))\nSelectScrollDownButton.displayName =\n  SelectPrimitive.ScrollDownButton.displayName\n\nconst SelectContent = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Content>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Content>\n>(({ className, children, position = \"popper\", ...props }, ref) => (\n  <SelectPrimitive.Portal>\n    <SelectPrimitive.Content\n      ref={ref}\n      className={cn(\n        \"relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2\",\n        position === \"popper\" &&\n          \"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1\",\n        className\n      )}\n      position={position}\n      {...props}\n    >\n      <SelectScrollUpButton />\n      <SelectPrimitive.Viewport\n        className={cn(\n          \"p-1\",\n          position === \"popper\" &&\n            \"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]\"\n        )}\n      >\n        {children}\n      </SelectPrimitive.Viewport>\n      <SelectScrollDownButton />\n    </SelectPrimitive.Content>\n  </SelectPrimitive.Portal>\n))\nSelectContent.displayName = SelectPrimitive.Content.displayName\n\nconst SelectLabel = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Label>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Label>\n>(({ className, ...props }, ref) => (\n  <SelectPrimitive.Label\n    ref={ref}\n    className={cn(\"py-1.5 pl-8 pr-2 text-sm font-semibold\", className)}\n    {...props}\n  />\n))\nSelectLabel.displayName = SelectPrimitive.Label.displayName\n\nconst SelectItem = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Item>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Item>\n>(({ className, children, ...props }, ref) => (\n  <SelectPrimitive.Item\n    ref={ref}\n    className={cn(\n      \"relative flex w-full cursor-default select-none items-center gap-2 rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50\",\n      className\n    )}\n    {...props}\n  >\n    <span className=\"absolute left-2 flex h-3.5 w-3.5 items-center justify-center\">\n      <SelectPrimitive.ItemIndicator>\n        <Check className=\"h-4 w-4\" />\n      </SelectPrimitive.ItemIndicator>\n    </span>\n\n    <SelectPrimitive.ItemText>{children}</SelectPrimitive.ItemText>\n  </SelectPrimitive.Item>\n))\nSelectItem.displayName = SelectPrimitive.Item.displayName\n\nconst SelectSeparator = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Separator>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Separator>\n>(({ className, ...props }, ref) => (\n  <SelectPrimitive.Separator\n    ref={ref}\n    className={cn(\"-mx-1 my-1 h-px bg-muted\", className)}\n    {...props}\n  />\n))\nSelectSeparator.displayName = SelectPrimitive.Separator.displayName\n\nexport {\n  Select,\n  SelectGroup,\n  SelectValue,\n  SelectTrigger,\n  SelectContent,\n  SelectLabel,\n  SelectItem,\n  SelectSeparator,\n  SelectScrollUpButton,\n  SelectScrollDownButton,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAGA;AACA;AACA;AAAA;AAAA;AAEA;AANA;;;;;;AAQA,MAAM,SAAS,kKAAA,CAAA,OAAoB;AAEnC,MAAM,cAAc,kKAAA,CAAA,QAAqB;AAEzC,MAAM,cAAc,kKAAA,CAAA,QAAqB;AAEzC,MAAM,8BAAgB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGnC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,8OAAC,kKAAA,CAAA,UAAuB;QACtB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,mTACA;QAED,GAAG,KAAK;;YAER;0BACD,8OAAC,kKAAA,CAAA,OAAoB;gBAAC,OAAO;0BAC3B,cAAA,8OAAC,oNAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAI7B,cAAc,WAAW,GAAG,kKAAA,CAAA,UAAuB,CAAC,WAAW;AAE/D,MAAM,qCAAuB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG1C,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAA,CAAA,iBAA8B;QAC7B,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC,gNAAA,CAAA,YAAS;YAAC,WAAU;;;;;;;;;;;AAGzB,qBAAqB,WAAW,GAAG,kKAAA,CAAA,iBAA8B,CAAC,WAAW;AAE7E,MAAM,uCAAyB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG5C,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAA,CAAA,mBAAgC;QAC/B,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC,oNAAA,CAAA,cAAW;YAAC,WAAU;;;;;;;;;;;AAG3B,uBAAuB,WAAW,GAChC,kKAAA,CAAA,mBAAgC,CAAC,WAAW;AAE9C,MAAM,8BAAgB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGnC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,WAAW,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACzD,8OAAC,kKAAA,CAAA,SAAsB;kBACrB,cAAA,8OAAC,kKAAA,CAAA,UAAuB;YACtB,KAAK;YACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,ucACA,aAAa,YACX,mIACF;YAEF,UAAU;YACT,GAAG,KAAK;;8BAET,8OAAC;;;;;8BACD,8OAAC,kKAAA,CAAA,WAAwB;oBACvB,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,OACA,aAAa,YACX;8BAGH;;;;;;8BAEH,8OAAC;;;;;;;;;;;;;;;;AAIP,cAAc,WAAW,GAAG,kKAAA,CAAA,UAAuB,CAAC,WAAW;AAE/D,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAA,CAAA,QAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,0CAA0C;QACvD,GAAG,KAAK;;;;;;AAGb,YAAY,WAAW,GAAG,kKAAA,CAAA,QAAqB,CAAC,WAAW;AAE3D,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,8OAAC,kKAAA,CAAA,OAAoB;QACnB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,mOACA;QAED,GAAG,KAAK;;0BAET,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC,kKAAA,CAAA,gBAA6B;8BAC5B,cAAA,8OAAC,oMAAA,CAAA,QAAK;wBAAC,WAAU;;;;;;;;;;;;;;;;0BAIrB,8OAAC,kKAAA,CAAA,WAAwB;0BAAE;;;;;;;;;;;;AAG/B,WAAW,WAAW,GAAG,kKAAA,CAAA,OAAoB,CAAC,WAAW;AAEzD,MAAM,gCAAkB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAA,CAAA,YAAyB;QACxB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,4BAA4B;QACzC,GAAG,KAAK;;;;;;AAGb,gBAAgB,WAAW,GAAG,kKAAA,CAAA,YAAyB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 1382, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/src/components/settings/language-settings.tsx"], "sourcesContent": ["\n\"use client\";\n\nimport { Label } from \"@/components/ui/label\";\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from \"@/components/ui/select\";\nimport { Languages, Globe, MapPin } from \"lucide-react\"; \nimport { useI18n } from \"@/contexts/i18n-context\";\nimport { APP_SUPPORTED_UI_LANGUAGES, APP_WRITING_LANGUAGES } from \"@/config/languages\";\nimport { useMemo } from \"react\";\n\nexport function LanguageSettings() {\n  const { \n    t, \n    uiLanguage, \n    setUiLanguage,\n    writingLanguageDialect,\n    setWritingLanguageDialect,\n    getWritingLanguageBase,\n  } = useI18n();\n\n  const currentWritingBaseLang = getWritingLanguageBase();\n\n  const handleWritingLanguageChange = (newBaseLang: string) => {\n    const langInfo = APP_WRITING_LANGUAGES.find(l => l.value === newBaseLang);\n    if (langInfo) {\n      // If it has dialects, pick the first one. Otherwise, use the base lang itself (e.g. for Arabic)\n      const newDialect = langInfo.dialects && langInfo.dialects.length > 0 ? langInfo.dialects[0].value : newBaseLang;\n      setWritingLanguageDialect(newDialect);\n    }\n  };\n\n  const handleDialectChange = (newDialect: string) => {\n    setWritingLanguageDialect(newDialect);\n  };\n\n  const currentWritingLanguageInfo = useMemo(() => {\n    return APP_WRITING_LANGUAGES.find(lang => lang.value === currentWritingBaseLang);\n  }, [currentWritingBaseLang]);\n\n  return (\n    <div className=\"space-y-6\">\n      <div>\n        <Label htmlFor=\"ui-language-select\" className=\"flex items-center text-sm font-medium mb-1\">\n          <Globe className=\"mr-2 h-4 w-4 text-primary\" />\n          {t('uiLanguageLabel')}\n        </Label>\n        <Select value={uiLanguage} onValueChange={setUiLanguage}>\n          <SelectTrigger id=\"ui-language-select\" className=\"w-full\">\n            <SelectValue placeholder={t('selectUiLanguagePlaceholder')} />\n          </SelectTrigger>\n          <SelectContent>\n            {APP_SUPPORTED_UI_LANGUAGES.map((lang) => (\n              <SelectItem key={lang.value} value={lang.value}>\n                {t(lang.labelKey)}\n              </SelectItem>\n            ))}\n          </SelectContent>\n        </Select>\n         <p className=\"text-xs text-muted-foreground mt-1 pl-1\">{t('uiLanguageDescription')}</p>\n      </div>\n\n      <div>\n        <Label htmlFor=\"writing-language-select\" className=\"flex items-center text-sm font-medium mb-1\">\n          <Languages className=\"mr-2 h-4 w-4 text-primary\" />\n          {t('writingLanguageLabel')}\n        </Label>\n        <Select value={currentWritingBaseLang} onValueChange={handleWritingLanguageChange}>\n          <SelectTrigger id=\"writing-language-select\" className=\"w-full\">\n            <SelectValue placeholder={t('selectWritingLanguagePlaceholder')} />\n          </SelectTrigger>\n          <SelectContent>\n            {APP_WRITING_LANGUAGES.map((lang) => (\n              <SelectItem key={lang.value} value={lang.value}>\n                {t(lang.labelKey)}\n              </SelectItem>\n            ))}\n          </SelectContent>\n        </Select>\n        <p className=\"text-xs text-muted-foreground mt-1 pl-1\">{t('writingLanguageDescription')}</p>\n      </div>\n\n      {currentWritingLanguageInfo?.dialects && currentWritingLanguageInfo.dialects.length > 0 && (\n        <div>\n          <Label htmlFor=\"regional-dialect-select\" className=\"flex items-center text-sm font-medium mb-1\">\n            <MapPin className=\"mr-2 h-4 w-4 text-primary\" />\n            {t('regionalDialectLabel')}\n          </Label>\n          <Select value={writingLanguageDialect} onValueChange={handleDialectChange}>\n            <SelectTrigger id=\"regional-dialect-select\" className=\"w-full\">\n              <SelectValue placeholder={t('selectRegionalDialectPlaceholder')} />\n            </SelectTrigger>\n            <SelectContent>\n              {currentWritingLanguageInfo.dialects.map((dialect) => (\n                <SelectItem key={dialect.value} value={dialect.value}>\n                  {t(dialect.labelKey)}\n                </SelectItem>\n              ))}\n            </SelectContent>\n          </Select>\n          <p className=\"text-xs text-muted-foreground mt-1 pl-1\">{t('regionalDialectDescription')}</p>\n        </div>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AAAA;AAAA;AACA;AACA;AACA;AAPA;;;;;;;;AASO,SAAS;IACd,MAAM,EACJ,CAAC,EACD,UAAU,EACV,aAAa,EACb,sBAAsB,EACtB,yBAAyB,EACzB,sBAAsB,EACvB,GAAG,CAAA,GAAA,mIAAA,CAAA,UAAO,AAAD;IAEV,MAAM,yBAAyB;IAE/B,MAAM,8BAA8B,CAAC;QACnC,MAAM,WAAW,0HAAA,CAAA,wBAAqB,CAAC,IAAI,CAAC,CAAA,IAAK,EAAE,KAAK,KAAK;QAC7D,IAAI,UAAU;YACZ,gGAAgG;YAChG,MAAM,aAAa,SAAS,QAAQ,IAAI,SAAS,QAAQ,CAAC,MAAM,GAAG,IAAI,SAAS,QAAQ,CAAC,EAAE,CAAC,KAAK,GAAG;YACpG,0BAA0B;QAC5B;IACF;IAEA,MAAM,sBAAsB,CAAC;QAC3B,0BAA0B;IAC5B;IAEA,MAAM,6BAA6B,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QACzC,OAAO,0HAAA,CAAA,wBAAqB,CAAC,IAAI,CAAC,CAAA,OAAQ,KAAK,KAAK,KAAK;IAC3D,GAAG;QAAC;KAAuB;IAE3B,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;;kCACC,8OAAC,iIAAA,CAAA,QAAK;wBAAC,SAAQ;wBAAqB,WAAU;;0CAC5C,8OAAC,oMAAA,CAAA,QAAK;gCAAC,WAAU;;;;;;4BAChB,EAAE;;;;;;;kCAEL,8OAAC,kIAAA,CAAA,SAAM;wBAAC,OAAO;wBAAY,eAAe;;0CACxC,8OAAC,kIAAA,CAAA,gBAAa;gCAAC,IAAG;gCAAqB,WAAU;0CAC/C,cAAA,8OAAC,kIAAA,CAAA,cAAW;oCAAC,aAAa,EAAE;;;;;;;;;;;0CAE9B,8OAAC,kIAAA,CAAA,gBAAa;0CACX,0HAAA,CAAA,6BAA0B,CAAC,GAAG,CAAC,CAAC,qBAC/B,8OAAC,kIAAA,CAAA,aAAU;wCAAkB,OAAO,KAAK,KAAK;kDAC3C,EAAE,KAAK,QAAQ;uCADD,KAAK,KAAK;;;;;;;;;;;;;;;;kCAMhC,8OAAC;wBAAE,WAAU;kCAA2C,EAAE;;;;;;;;;;;;0BAG7D,8OAAC;;kCACC,8OAAC,iIAAA,CAAA,QAAK;wBAAC,SAAQ;wBAA0B,WAAU;;0CACjD,8OAAC,4MAAA,CAAA,YAAS;gCAAC,WAAU;;;;;;4BACpB,EAAE;;;;;;;kCAEL,8OAAC,kIAAA,CAAA,SAAM;wBAAC,OAAO;wBAAwB,eAAe;;0CACpD,8OAAC,kIAAA,CAAA,gBAAa;gCAAC,IAAG;gCAA0B,WAAU;0CACpD,cAAA,8OAAC,kIAAA,CAAA,cAAW;oCAAC,aAAa,EAAE;;;;;;;;;;;0CAE9B,8OAAC,kIAAA,CAAA,gBAAa;0CACX,0HAAA,CAAA,wBAAqB,CAAC,GAAG,CAAC,CAAC,qBAC1B,8OAAC,kIAAA,CAAA,aAAU;wCAAkB,OAAO,KAAK,KAAK;kDAC3C,EAAE,KAAK,QAAQ;uCADD,KAAK,KAAK;;;;;;;;;;;;;;;;kCAMjC,8OAAC;wBAAE,WAAU;kCAA2C,EAAE;;;;;;;;;;;;YAG3D,4BAA4B,YAAY,2BAA2B,QAAQ,CAAC,MAAM,GAAG,mBACpF,8OAAC;;kCACC,8OAAC,iIAAA,CAAA,QAAK;wBAAC,SAAQ;wBAA0B,WAAU;;0CACjD,8OAAC,0MAAA,CAAA,SAAM;gCAAC,WAAU;;;;;;4BACjB,EAAE;;;;;;;kCAEL,8OAAC,kIAAA,CAAA,SAAM;wBAAC,OAAO;wBAAwB,eAAe;;0CACpD,8OAAC,kIAAA,CAAA,gBAAa;gCAAC,IAAG;gCAA0B,WAAU;0CACpD,cAAA,8OAAC,kIAAA,CAAA,cAAW;oCAAC,aAAa,EAAE;;;;;;;;;;;0CAE9B,8OAAC,kIAAA,CAAA,gBAAa;0CACX,2BAA2B,QAAQ,CAAC,GAAG,CAAC,CAAC,wBACxC,8OAAC,kIAAA,CAAA,aAAU;wCAAqB,OAAO,QAAQ,KAAK;kDACjD,EAAE,QAAQ,QAAQ;uCADJ,QAAQ,KAAK;;;;;;;;;;;;;;;;kCAMpC,8OAAC;wBAAE,WAAU;kCAA2C,EAAE;;;;;;;;;;;;;;;;;;AAKpE", "debugId": null}}, {"offset": {"line": 1656, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/src/components/ui/switch.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as SwitchPrimitives from \"@radix-ui/react-switch\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Switch = React.forwardRef<\n  React.ElementRef<typeof SwitchPrimitives.Root>,\n  React.ComponentPropsWithoutRef<typeof SwitchPrimitives.Root>\n>(({ className, ...props }, ref) => (\n  <SwitchPrimitives.Root\n    className={cn(\n      \"peer inline-flex h-6 w-11 shrink-0 cursor-pointer items-center rounded-full border-2 border-transparent transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:ring-offset-background disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=unchecked]:bg-input\",\n      className\n    )}\n    {...props}\n    ref={ref}\n  >\n    <SwitchPrimitives.Thumb\n      className={cn(\n        \"pointer-events-none block h-5 w-5 rounded-full bg-background shadow-lg ring-0 transition-transform data-[state=checked]:translate-x-5 data-[state=unchecked]:translate-x-0\"\n      )}\n    />\n  </SwitchPrimitives.Root>\n))\nSwitch.displayName = SwitchPrimitives.Root.displayName\n\nexport { Switch }\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAEA;AALA;;;;;AAOA,MAAM,uBAAS,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG5B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAA,CAAA,OAAqB;QACpB,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,sXACA;QAED,GAAG,KAAK;QACT,KAAK;kBAEL,cAAA,8OAAC,kKAAA,CAAA,QAAsB;YACrB,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV;;;;;;;;;;;AAKR,OAAO,WAAW,GAAG,kKAAA,CAAA,OAAqB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 1692, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/src/components/settings/appearance-settings.tsx"], "sourcesContent": ["\n\"use client\";\n\nimport { <PERSON><PERSON> } from \"@/components/ui/button\";\nimport { Label } from \"@/components/ui/label\";\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from \"@/components/ui/select\";\nimport { Switch } from \"@/components/ui/switch\";\nimport { Pa<PERSON>, <PERSON>, Sun, Type, Contrast } from \"lucide-react\"; // Changed FontSize to Type\nimport { useAppearance, type FontSizeSetting, type ThemeSetting } from \"@/contexts/theme-context\"; // Use new name\nimport { useI18n } from \"@/contexts/i18n-context\";\n\nconst themeOptions: Array<{value: ThemeSetting, labelKey: string, icon?: JSX.Element}> = [\n    { value: \"light\", labelKey: \"themeLight\", icon: <Sun className=\"mr-2 h-4 w-4\" /> },\n    { value: \"dark\", labelKey: \"themeDark\", icon: <Moon className=\"mr-2 h-4 w-4\" /> },\n    { value: \"system\", labelKey: \"themeSystem\", icon: <Palette className=\"mr-2 h-4 w-4\" /> },\n];\n\nconst fontSizeOptions: Array<{value: FontSizeSetting, labelKey: string}> = [\n    { value: \"small\", labelKey: \"fontSizeSmall\" },\n    { value: \"medium\", labelKey: \"fontSizeMedium\" },\n    { value: \"large\", labelKey: \"fontSizeLarge\" },\n];\n\nexport function AppearanceSettings() {\n  const { t } = useI18n();\n  const { \n    theme, \n    setTheme, \n    fontSize, \n    setFontSize, \n    isHighContrast, \n    toggleHighContrast,\n    effectiveTheme \n  } = useAppearance();\n  \n  return (\n    <div className=\"space-y-6\">\n      <div>\n        <Label htmlFor=\"theme-select\" className=\"flex items-center text-sm font-medium mb-1\">\n          <Palette className=\"mr-2 h-4 w-4 text-primary\" />\n          {t('themeLabel')}\n        </Label>\n        <Select value={theme} onValueChange={(value) => setTheme(value as ThemeSetting)}>\n            <SelectTrigger id=\"theme-select\" className=\"w-full\">\n                <SelectValue placeholder={t('selectThemePlaceholder')} />\n            </SelectTrigger>\n            <SelectContent>\n                {themeOptions.map(opt => (\n                    <SelectItem key={opt.value} value={opt.value}>\n                        <div className=\"flex items-center\">\n                            {opt.icon}\n                            {t(opt.labelKey)}\n                        </div>\n                    </SelectItem>\n                ))}\n            </SelectContent>\n        </Select>\n        <p className=\"text-xs text-muted-foreground mt-1 pl-1\">{t('themeDescription')}</p>\n      </div>\n\n      <div>\n        <Label htmlFor=\"font-size-select\" className=\"flex items-center text-sm font-medium mb-1\">\n          <Type className=\"mr-2 h-4 w-4 text-primary\" /> \n          {t('fontSizeLabel')}\n        </Label>\n        <Select value={fontSize} onValueChange={(value) => setFontSize(value as FontSizeSetting)}>\n            <SelectTrigger id=\"font-size-select\" className=\"w-full\">\n                <SelectValue placeholder={t('selectFontSizePlaceholder')} />\n            </SelectTrigger>\n            <SelectContent>\n                {fontSizeOptions.map(opt => (\n                    <SelectItem key={opt.value} value={opt.value}>\n                        {t(opt.labelKey)}\n                    </SelectItem>\n                ))}\n            </SelectContent>\n        </Select>\n        <p className=\"text-xs text-muted-foreground mt-1 pl-1\">{t('fontSizeDescription')}</p>\n      </div>\n      \n      <div>\n        <Label htmlFor=\"high-contrast-switch\" className=\"flex items-center text-sm font-medium mb-1\">\n            <Contrast className=\"mr-2 h-4 w-4 text-primary\" />\n            {t('highContrastModeLabel')}\n        </Label>\n        <div className=\"flex items-center space-x-2 pl-1\">\n            <Switch\n                id=\"high-contrast-switch\"\n                checked={isHighContrast}\n                onCheckedChange={toggleHighContrast}\n                aria-label={t('highContrastModeLabel')}\n            />\n            <span className=\"text-sm text-muted-foreground\">\n                {isHighContrast ? t('enabledLabel') : t('disabledLabel')}\n            </span>\n        </div>\n        <p className=\"text-xs text-muted-foreground mt-1 pl-1\">{t('highContrastModeDescription')}</p>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAIA;AACA;AACA;AACA,4VAAmE,2BAA2B;AAA9F;AAAA;AAAA;AAAA;AACA,uOAAmG,eAAe;AAClH;AARA;;;;;;;;AAUA,MAAM,eAAmF;IACrF;QAAE,OAAO;QAAS,UAAU;QAAc,oBAAM,8OAAC,gMAAA,CAAA,MAAG;YAAC,WAAU;;;;;;IAAkB;IACjF;QAAE,OAAO;QAAQ,UAAU;QAAa,oBAAM,8OAAC,kMAAA,CAAA,OAAI;YAAC,WAAU;;;;;;IAAkB;IAChF;QAAE,OAAO;QAAU,UAAU;QAAe,oBAAM,8OAAC,wMAAA,CAAA,UAAO;YAAC,WAAU;;;;;;IAAkB;CAC1F;AAED,MAAM,kBAAqE;IACvE;QAAE,OAAO;QAAS,UAAU;IAAgB;IAC5C;QAAE,OAAO;QAAU,UAAU;IAAiB;IAC9C;QAAE,OAAO;QAAS,UAAU;IAAgB;CAC/C;AAEM,SAAS;IACd,MAAM,EAAE,CAAC,EAAE,GAAG,CAAA,GAAA,mIAAA,CAAA,UAAO,AAAD;IACpB,MAAM,EACJ,KAAK,EACL,QAAQ,EACR,QAAQ,EACR,WAAW,EACX,cAAc,EACd,kBAAkB,EAClB,cAAc,EACf,GAAG,CAAA,GAAA,oIAAA,CAAA,gBAAa,AAAD;IAEhB,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;;kCACC,8OAAC,iIAAA,CAAA,QAAK;wBAAC,SAAQ;wBAAe,WAAU;;0CACtC,8OAAC,wMAAA,CAAA,UAAO;gCAAC,WAAU;;;;;;4BAClB,EAAE;;;;;;;kCAEL,8OAAC,kIAAA,CAAA,SAAM;wBAAC,OAAO;wBAAO,eAAe,CAAC,QAAU,SAAS;;0CACrD,8OAAC,kIAAA,CAAA,gBAAa;gCAAC,IAAG;gCAAe,WAAU;0CACvC,cAAA,8OAAC,kIAAA,CAAA,cAAW;oCAAC,aAAa,EAAE;;;;;;;;;;;0CAEhC,8OAAC,kIAAA,CAAA,gBAAa;0CACT,aAAa,GAAG,CAAC,CAAA,oBACd,8OAAC,kIAAA,CAAA,aAAU;wCAAiB,OAAO,IAAI,KAAK;kDACxC,cAAA,8OAAC;4CAAI,WAAU;;gDACV,IAAI,IAAI;gDACR,EAAE,IAAI,QAAQ;;;;;;;uCAHN,IAAI,KAAK;;;;;;;;;;;;;;;;kCAStC,8OAAC;wBAAE,WAAU;kCAA2C,EAAE;;;;;;;;;;;;0BAG5D,8OAAC;;kCACC,8OAAC,iIAAA,CAAA,QAAK;wBAAC,SAAQ;wBAAmB,WAAU;;0CAC1C,8OAAC,kMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;4BACf,EAAE;;;;;;;kCAEL,8OAAC,kIAAA,CAAA,SAAM;wBAAC,OAAO;wBAAU,eAAe,CAAC,QAAU,YAAY;;0CAC3D,8OAAC,kIAAA,CAAA,gBAAa;gCAAC,IAAG;gCAAmB,WAAU;0CAC3C,cAAA,8OAAC,kIAAA,CAAA,cAAW;oCAAC,aAAa,EAAE;;;;;;;;;;;0CAEhC,8OAAC,kIAAA,CAAA,gBAAa;0CACT,gBAAgB,GAAG,CAAC,CAAA,oBACjB,8OAAC,kIAAA,CAAA,aAAU;wCAAiB,OAAO,IAAI,KAAK;kDACvC,EAAE,IAAI,QAAQ;uCADF,IAAI,KAAK;;;;;;;;;;;;;;;;kCAMtC,8OAAC;wBAAE,WAAU;kCAA2C,EAAE;;;;;;;;;;;;0BAG5D,8OAAC;;kCACC,8OAAC,iIAAA,CAAA,QAAK;wBAAC,SAAQ;wBAAuB,WAAU;;0CAC5C,8OAAC,0MAAA,CAAA,WAAQ;gCAAC,WAAU;;;;;;4BACnB,EAAE;;;;;;;kCAEP,8OAAC;wBAAI,WAAU;;0CACX,8OAAC,kIAAA,CAAA,SAAM;gCACH,IAAG;gCACH,SAAS;gCACT,iBAAiB;gCACjB,cAAY,EAAE;;;;;;0CAElB,8OAAC;gCAAK,WAAU;0CACX,iBAAiB,EAAE,kBAAkB,EAAE;;;;;;;;;;;;kCAGhD,8OAAC;wBAAE,WAAU;kCAA2C,EAAE;;;;;;;;;;;;;;;;;;AAIlE", "debugId": null}}, {"offset": {"line": 1999, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/src/components/ui/scroll-area.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as ScrollAreaPrimitive from \"@radix-ui/react-scroll-area\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst ScrollArea = React.forwardRef<\n  React.ElementRef<typeof ScrollAreaPrimitive.Root>,\n  React.ComponentPropsWithoutRef<typeof ScrollAreaPrimitive.Root>\n>(({ className, children, ...props }, ref) => (\n  <ScrollAreaPrimitive.Root\n    ref={ref}\n    className={cn(\"relative overflow-hidden\", className)}\n    {...props}\n  >\n    <ScrollAreaPrimitive.Viewport className=\"h-full w-full rounded-[inherit]\">\n      {children}\n    </ScrollAreaPrimitive.Viewport>\n    <ScrollBar />\n    <ScrollAreaPrimitive.Corner />\n  </ScrollAreaPrimitive.Root>\n))\nScrollArea.displayName = ScrollAreaPrimitive.Root.displayName\n\nconst ScrollBar = React.forwardRef<\n  React.ElementRef<typeof ScrollAreaPrimitive.ScrollAreaScrollbar>,\n  React.ComponentPropsWithoutRef<typeof ScrollAreaPrimitive.ScrollAreaScrollbar>\n>(({ className, orientation = \"vertical\", ...props }, ref) => (\n  <ScrollAreaPrimitive.ScrollAreaScrollbar\n    ref={ref}\n    orientation={orientation}\n    className={cn(\n      \"flex touch-none select-none transition-colors\",\n      orientation === \"vertical\" &&\n        \"h-full w-2.5 border-l border-l-transparent p-[1px]\",\n      orientation === \"horizontal\" &&\n        \"h-2.5 flex-col border-t border-t-transparent p-[1px]\",\n      className\n    )}\n    {...props}\n  >\n    <ScrollAreaPrimitive.ScrollAreaThumb className=\"relative flex-1 rounded-full bg-border\" />\n  </ScrollAreaPrimitive.ScrollAreaScrollbar>\n))\nScrollBar.displayName = ScrollAreaPrimitive.ScrollAreaScrollbar.displayName\n\nexport { ScrollArea, ScrollBar }\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AAEA;AALA;;;;;AAOA,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,8OAAC,0KAAA,CAAA,OAAwB;QACvB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,4BAA4B;QACzC,GAAG,KAAK;;0BAET,8OAAC,0KAAA,CAAA,WAA4B;gBAAC,WAAU;0BACrC;;;;;;0BAEH,8OAAC;;;;;0BACD,8OAAC,0KAAA,CAAA,SAA0B;;;;;;;;;;;AAG/B,WAAW,WAAW,GAAG,0KAAA,CAAA,OAAwB,CAAC,WAAW;AAE7D,MAAM,0BAAY,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG/B,CAAC,EAAE,SAAS,EAAE,cAAc,UAAU,EAAE,GAAG,OAAO,EAAE,oBACpD,8OAAC,0KAAA,CAAA,sBAAuC;QACtC,KAAK;QACL,aAAa;QACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,iDACA,gBAAgB,cACd,sDACF,gBAAgB,gBACd,wDACF;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC,0KAAA,CAAA,kBAAmC;YAAC,WAAU;;;;;;;;;;;AAGnD,UAAU,WAAW,GAAG,0KAAA,CAAA,sBAAuC,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 2067, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/src/components/ui/alert-dialog.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as AlertDialogPrimitive from \"@radix-ui/react-alert-dialog\"\n\nimport { cn } from \"@/lib/utils\"\nimport { buttonVariants } from \"@/components/ui/button\"\n\nconst AlertDialog = AlertDialogPrimitive.Root\n\nconst AlertDialogTrigger = AlertDialogPrimitive.Trigger\n\nconst AlertDialogPortal = AlertDialogPrimitive.Portal\n\nconst AlertDialogOverlay = React.forwardRef<\n  React.ElementRef<typeof AlertDialogPrimitive.Overlay>,\n  React.ComponentPropsWithoutRef<typeof AlertDialogPrimitive.Overlay>\n>(({ className, ...props }, ref) => (\n  <AlertDialogPrimitive.Overlay\n    className={cn(\n      \"fixed inset-0 z-50 bg-black/80  data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0\",\n      className\n    )}\n    {...props}\n    ref={ref}\n  />\n))\nAlertDialogOverlay.displayName = AlertDialogPrimitive.Overlay.displayName\n\nconst AlertDialogContent = React.forwardRef<\n  React.ElementRef<typeof AlertDialogPrimitive.Content>,\n  React.ComponentPropsWithoutRef<typeof AlertDialogPrimitive.Content>\n>(({ className, ...props }, ref) => (\n  <AlertDialogPortal>\n    <AlertDialogOverlay />\n    <AlertDialogPrimitive.Content\n      ref={ref}\n      className={cn(\n        \"fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg\",\n        className\n      )}\n      {...props}\n    />\n  </AlertDialogPortal>\n))\nAlertDialogContent.displayName = AlertDialogPrimitive.Content.displayName\n\nconst AlertDialogHeader = ({\n  className,\n  ...props\n}: React.HTMLAttributes<HTMLDivElement>) => (\n  <div\n    className={cn(\n      \"flex flex-col space-y-2 text-center sm:text-left\",\n      className\n    )}\n    {...props}\n  />\n)\nAlertDialogHeader.displayName = \"AlertDialogHeader\"\n\nconst AlertDialogFooter = ({\n  className,\n  ...props\n}: React.HTMLAttributes<HTMLDivElement>) => (\n  <div\n    className={cn(\n      \"flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2\",\n      className\n    )}\n    {...props}\n  />\n)\nAlertDialogFooter.displayName = \"AlertDialogFooter\"\n\nconst AlertDialogTitle = React.forwardRef<\n  React.ElementRef<typeof AlertDialogPrimitive.Title>,\n  React.ComponentPropsWithoutRef<typeof AlertDialogPrimitive.Title>\n>(({ className, ...props }, ref) => (\n  <AlertDialogPrimitive.Title\n    ref={ref}\n    className={cn(\"text-lg font-semibold\", className)}\n    {...props}\n  />\n))\nAlertDialogTitle.displayName = AlertDialogPrimitive.Title.displayName\n\nconst AlertDialogDescription = React.forwardRef<\n  React.ElementRef<typeof AlertDialogPrimitive.Description>,\n  React.ComponentPropsWithoutRef<typeof AlertDialogPrimitive.Description>\n>(({ className, ...props }, ref) => (\n  <AlertDialogPrimitive.Description\n    ref={ref}\n    className={cn(\"text-sm text-muted-foreground\", className)}\n    {...props}\n  />\n))\nAlertDialogDescription.displayName =\n  AlertDialogPrimitive.Description.displayName\n\nconst AlertDialogAction = React.forwardRef<\n  React.ElementRef<typeof AlertDialogPrimitive.Action>,\n  React.ComponentPropsWithoutRef<typeof AlertDialogPrimitive.Action>\n>(({ className, ...props }, ref) => (\n  <AlertDialogPrimitive.Action\n    ref={ref}\n    className={cn(buttonVariants(), className)}\n    {...props}\n  />\n))\nAlertDialogAction.displayName = AlertDialogPrimitive.Action.displayName\n\nconst AlertDialogCancel = React.forwardRef<\n  React.ElementRef<typeof AlertDialogPrimitive.Cancel>,\n  React.ComponentPropsWithoutRef<typeof AlertDialogPrimitive.Cancel>\n>(({ className, ...props }, ref) => (\n  <AlertDialogPrimitive.Cancel\n    ref={ref}\n    className={cn(\n      buttonVariants({ variant: \"outline\" }),\n      \"mt-2 sm:mt-0\",\n      className\n    )}\n    {...props}\n  />\n))\nAlertDialogCancel.displayName = AlertDialogPrimitive.Cancel.displayName\n\nexport {\n  AlertDialog,\n  AlertDialogPortal,\n  AlertDialogOverlay,\n  AlertDialogTrigger,\n  AlertDialogContent,\n  AlertDialogHeader,\n  AlertDialogFooter,\n  AlertDialogTitle,\n  AlertDialogDescription,\n  AlertDialogAction,\n  AlertDialogCancel,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;AAEA;AACA;AAEA;AACA;AANA;;;;;;AAQA,MAAM,cAAc,2KAAA,CAAA,OAAyB;AAE7C,MAAM,qBAAqB,2KAAA,CAAA,UAA4B;AAEvD,MAAM,oBAAoB,2KAAA,CAAA,SAA2B;AAErD,MAAM,mCAAqB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGxC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,2KAAA,CAAA,UAA4B;QAC3B,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,2JACA;QAED,GAAG,KAAK;QACT,KAAK;;;;;;AAGT,mBAAmB,WAAW,GAAG,2KAAA,CAAA,UAA4B,CAAC,WAAW;AAEzE,MAAM,mCAAqB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGxC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;;0BACC,8OAAC;;;;;0BACD,8OAAC,2KAAA,CAAA,UAA4B;gBAC3B,KAAK;gBACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,+fACA;gBAED,GAAG,KAAK;;;;;;;;;;;;AAIf,mBAAmB,WAAW,GAAG,2KAAA,CAAA,UAA4B,CAAC,WAAW;AAEzE,MAAM,oBAAoB,CAAC,EACzB,SAAS,EACT,GAAG,OACkC,iBACrC,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,oDACA;QAED,GAAG,KAAK;;;;;;AAGb,kBAAkB,WAAW,GAAG;AAEhC,MAAM,oBAAoB,CAAC,EACzB,SAAS,EACT,GAAG,OACkC,iBACrC,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,iEACA;QAED,GAAG,KAAK;;;;;;AAGb,kBAAkB,WAAW,GAAG;AAEhC,MAAM,iCAAmB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGtC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,2KAAA,CAAA,QAA0B;QACzB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,yBAAyB;QACtC,GAAG,KAAK;;;;;;AAGb,iBAAiB,WAAW,GAAG,2KAAA,CAAA,QAA0B,CAAC,WAAW;AAErE,MAAM,uCAAyB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG5C,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,2KAAA,CAAA,cAAgC;QAC/B,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,uBAAuB,WAAW,GAChC,2KAAA,CAAA,cAAgC,CAAC,WAAW;AAE9C,MAAM,kCAAoB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGvC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,2KAAA,CAAA,SAA2B;QAC1B,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,CAAA,GAAA,kIAAA,CAAA,iBAAc,AAAD,KAAK;QAC/B,GAAG,KAAK;;;;;;AAGb,kBAAkB,WAAW,GAAG,2KAAA,CAAA,SAA2B,CAAC,WAAW;AAEvE,MAAM,kCAAoB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGvC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,2KAAA,CAAA,SAA2B;QAC1B,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,CAAA,GAAA,kIAAA,CAAA,iBAAc,AAAD,EAAE;YAAE,SAAS;QAAU,IACpC,gBACA;QAED,GAAG,KAAK;;;;;;AAGb,kBAAkB,WAAW,GAAG,2KAAA,CAAA,SAA2B,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 2194, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/src/components/settings/dictionary-settings.tsx"], "sourcesContent": ["\n\"use client\";\n\nimport { useState, type FormEvent, useRef, useMemo } from 'react';\nimport { But<PERSON> } from \"@/components/ui/button\";\nimport { Input } from \"@/components/ui/input\";\nimport { Label } from \"@/components/ui/label\";\nimport { ScrollArea } from \"@/components/ui/scroll-area\";\nimport { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from \"@/components/ui/alert-dialog\";\nimport { BookMarked, PlusCircle, Trash2, Upload, Download, FileJson, XCircle, Languages } from \"lucide-react\";\nimport { useDictionary } from \"@/contexts/dictionary-context\";\nimport { useI18n } from \"@/contexts/i18n-context\";\nimport { useToast } from '@/hooks/use-toast';\nimport { APP_WRITING_LANGUAGES } from '@/config/languages';\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../ui/select';\n\nexport function DictionarySettings() {\n  const { t, getWritingLanguageBase } = useI18n();\n  const { getWordsForLanguage, addWord, deleteWord, importWords, exportWordsString, clearDictionary } = useDictionary();\n  const [newWord, setNewWord] = useState(\"\");\n  const fileInputRef = useRef<HTMLInputElement>(null);\n  const { toast } = useToast();\n  \n  const [selectedLanguage, setSelectedLanguage] = useState(getWritingLanguageBase());\n\n  const wordsForSelectedLang = useMemo(() => getWordsForLanguage(selectedLanguage), [getWordsForLanguage, selectedLanguage]);\n\n  const handleAddWord = (event: FormEvent) => {\n    event.preventDefault();\n    if (newWord.trim()) {\n      if(addWord(newWord.trim(), selectedLanguage)) {\n        setNewWord(\"\");\n      }\n    }\n  };\n\n  const handleImport = (event: React.ChangeEvent<HTMLInputElement>) => {\n    const file = event.target.files?.[0];\n    if (file) {\n      const reader = new FileReader();\n      reader.onload = (e) => {\n        try {\n          const content = e.target?.result;\n          if (typeof content === 'string') {\n            const importedArray = JSON.parse(content);\n            if (Array.isArray(importedArray) && importedArray.every(item => typeof item === 'string')) {\n              importWords(importedArray, selectedLanguage, false); // false for merge\n            } else {\n              toast({titleKey: \"toastDictionaryImportInvalidFormat\", variant: \"destructive\"});\n            }\n          }\n        } catch (error) {\n          toast({titleKey: \"toastErrorTitle\", descriptionKey: \"toastDictionaryImportError\", variant: \"destructive\"});\n          console.error(\"Error importing dictionary:\", error);\n        }\n      };\n      reader.readAsText(file);\n    }\n    if (fileInputRef.current) {\n        fileInputRef.current.value = \"\";\n    }\n  };\n\n  const handleExport = () => {\n    const jsonString = exportWordsString(selectedLanguage);\n    const blob = new Blob([jsonString], { type: \"application/json\" });\n    const url = URL.createObjectURL(blob);\n    const a = document.createElement(\"a\");\n    a.href = url;\n    a.download = `lingua_flow_dictionary_${selectedLanguage}.json`;\n    document.body.appendChild(a);\n    a.click();\n    document.body.removeChild(a);\n    URL.revokeObjectURL(url);\n    toast({titleKey: \"toastSuccessTitle\", descriptionKey: \"toastDictionaryExportSuccess\"});\n  };\n  \n  const handleClearDictionary = () => {\n    clearDictionary(selectedLanguage);\n  };\n\n  const selectedLanguageInfo = APP_WRITING_LANGUAGES.find(lang => lang.value === selectedLanguage);\n  const selectedLanguageLabel = selectedLanguageInfo ? t(selectedLanguageInfo.labelKey) : selectedLanguage;\n\n  return (\n    <div className=\"space-y-6\">\n       <div>\n        <Label htmlFor=\"dictionary-language-select\" className=\"flex items-center text-sm font-medium mb-1\">\n          <Languages className=\"mr-2 h-4 w-4 text-primary\" />\n          {t('dictionaryLanguageLabel')}\n        </Label>\n        <Select value={selectedLanguage} onValueChange={setSelectedLanguage}>\n          <SelectTrigger id=\"dictionary-language-select\" className=\"w-full\">\n            <SelectValue placeholder={t('selectDictionaryLanguagePlaceholder')} />\n          </SelectTrigger>\n          <SelectContent>\n            {APP_WRITING_LANGUAGES.map((lang) => (\n              <SelectItem key={lang.value} value={lang.value}>\n                {t(lang.labelKey)}\n              </SelectItem>\n            ))}\n          </SelectContent>\n        </Select>\n         <p className=\"text-xs text-muted-foreground mt-1 pl-1\">{t('dictionaryLanguageDescription')}</p>\n      </div>\n\n      <div>\n        <Label className=\"flex items-center text-sm font-medium mb-2\">\n            <BookMarked className=\"mr-2 h-4 w-4 text-primary\" />\n            {t('personalDictionaryLabel')}\n        </Label>\n        <p className=\"text-xs text-muted-foreground mb-3 pl-1\">{t('personalDictionaryDescription')}</p>\n\n        <form onSubmit={handleAddWord} className=\"flex items-center gap-2 mb-4\">\n          <Input\n            type=\"text\"\n            value={newWord}\n            onChange={(e) => setNewWord(e.target.value)}\n            placeholder={t('addWordPlaceholder')}\n            className=\"flex-grow\"\n            aria-label={t('addWordPlaceholder')}\n          />\n          <Button type=\"submit\" size=\"icon\" variant=\"outline\" aria-label={t('addWordButton')}>\n            <PlusCircle className=\"h-4 w-4\" />\n          </Button>\n        </form>\n\n        {wordsForSelectedLang.length > 0 ? (\n          <ScrollArea className=\"h-40 w-full rounded-md border p-2 bg-muted/30\">\n            <ul className=\"space-y-1\">\n              {wordsForSelectedLang.map((word) => (\n                <li key={word} className=\"flex items-center justify-between p-1.5 rounded hover:bg-muted\">\n                  <span className=\"text-sm\">{word}</span>\n                  <Button variant=\"ghost\" size=\"icon\" className=\"h-6 w-6\" onClick={() => deleteWord(word, selectedLanguage)} aria-label={t('deleteWordButtonAria', { word })}>\n                    <XCircle className=\"h-4 w-4 text-destructive/80 hover:text-destructive\" />\n                  </Button>\n                </li>\n              ))}\n            </ul>\n          </ScrollArea>\n        ) : (\n          <p className=\"text-sm text-muted-foreground text-center py-4 border rounded-md bg-muted/30\">{t('dictionaryEmptyPlaceholder')}</p>\n        )}\n      </div>\n      \n      <div className=\"space-y-2\">\n         <Label className=\"flex items-center text-sm font-medium\">\n            <FileJson className=\"mr-2 h-4 w-4 text-primary\" />\n            {t('dictionaryImportExportLabel')}\n        </Label>\n        <div className=\"grid grid-cols-2 gap-2 pt-1\">\n            <Button variant=\"outline\" onClick={() => fileInputRef.current?.click()}>\n                <Upload className=\"mr-2 h-4 w-4\" /> {t('importDictionaryButton')}\n            </Button>\n            <input type=\"file\" accept=\".json\" ref={fileInputRef} onChange={handleImport} className=\"hidden\" />\n            \n            <Button variant=\"outline\" onClick={handleExport} disabled={wordsForSelectedLang.length === 0}>\n                <Download className=\"mr-2 h-4 w-4\" /> {t('exportDictionaryButton')}\n            </Button>\n        </div>\n         <p className=\"text-xs text-muted-foreground mt-1 pl-1\">{t('dictionaryImportExportDescription')}</p>\n      </div>\n\n      <div>\n        <AlertDialog>\n          <AlertDialogTrigger asChild>\n            <Button variant=\"destructive\" className=\"w-full\" disabled={wordsForSelectedLang.length === 0}>\n              <Trash2 className=\"mr-2 h-4 w-4\" /> {t('clearDictionaryForLanguageButton', { language: selectedLanguageLabel })}\n            </Button>\n          </AlertDialogTrigger>\n          <AlertDialogContent>\n            <AlertDialogHeader>\n              <AlertDialogTitle>{t('clearDictionaryConfirmTitle')}</AlertDialogTitle>\n              <AlertDialogDescription>\n                {t('clearDictionaryForLanguageConfirmDescription', { language: selectedLanguageLabel })}\n              </AlertDialogDescription>\n            </AlertDialogHeader>\n            <AlertDialogFooter>\n              <AlertDialogCancel>{t('cancelButton')}</AlertDialogCancel>\n              <AlertDialogAction onClick={handleClearDictionary} className=\"bg-destructive hover:bg-destructive/90\">\n                {t('confirmClearButton')}\n              </AlertDialogAction>\n            </AlertDialogFooter>\n          </AlertDialogContent>\n        </AlertDialog>\n        <p className=\"text-xs text-muted-foreground mt-1 pl-1 text-center\">{t('clearDictionaryWarning')}</p>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AAbA;;;;;;;;;;;;;;AAeO,SAAS;IACd,MAAM,EAAE,CAAC,EAAE,sBAAsB,EAAE,GAAG,CAAA,GAAA,mIAAA,CAAA,UAAO,AAAD;IAC5C,MAAM,EAAE,mBAAmB,EAAE,OAAO,EAAE,UAAU,EAAE,WAAW,EAAE,iBAAiB,EAAE,eAAe,EAAE,GAAG,CAAA,GAAA,yIAAA,CAAA,gBAAa,AAAD;IAClH,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAoB;IAC9C,MAAM,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,4HAAA,CAAA,WAAQ,AAAD;IAEzB,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEzD,MAAM,uBAAuB,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE,IAAM,oBAAoB,mBAAmB;QAAC;QAAqB;KAAiB;IAEzH,MAAM,gBAAgB,CAAC;QACrB,MAAM,cAAc;QACpB,IAAI,QAAQ,IAAI,IAAI;YAClB,IAAG,QAAQ,QAAQ,IAAI,IAAI,mBAAmB;gBAC5C,WAAW;YACb;QACF;IACF;IAEA,MAAM,eAAe,CAAC;QACpB,MAAM,OAAO,MAAM,MAAM,CAAC,KAAK,EAAE,CAAC,EAAE;QACpC,IAAI,MAAM;YACR,MAAM,SAAS,IAAI;YACnB,OAAO,MAAM,GAAG,CAAC;gBACf,IAAI;oBACF,MAAM,UAAU,EAAE,MAAM,EAAE;oBAC1B,IAAI,OAAO,YAAY,UAAU;wBAC/B,MAAM,gBAAgB,KAAK,KAAK,CAAC;wBACjC,IAAI,MAAM,OAAO,CAAC,kBAAkB,cAAc,KAAK,CAAC,CAAA,OAAQ,OAAO,SAAS,WAAW;4BACzF,YAAY,eAAe,kBAAkB,QAAQ,kBAAkB;wBACzE,OAAO;4BACL,MAAM;gCAAC,UAAU;gCAAsC,SAAS;4BAAa;wBAC/E;oBACF;gBACF,EAAE,OAAO,OAAO;oBACd,MAAM;wBAAC,UAAU;wBAAmB,gBAAgB;wBAA8B,SAAS;oBAAa;oBACxG,QAAQ,KAAK,CAAC,+BAA+B;gBAC/C;YACF;YACA,OAAO,UAAU,CAAC;QACpB;QACA,IAAI,aAAa,OAAO,EAAE;YACtB,aAAa,OAAO,CAAC,KAAK,GAAG;QACjC;IACF;IAEA,MAAM,eAAe;QACnB,MAAM,aAAa,kBAAkB;QACrC,MAAM,OAAO,IAAI,KAAK;YAAC;SAAW,EAAE;YAAE,MAAM;QAAmB;QAC/D,MAAM,MAAM,IAAI,eAAe,CAAC;QAChC,MAAM,IAAI,SAAS,aAAa,CAAC;QACjC,EAAE,IAAI,GAAG;QACT,EAAE,QAAQ,GAAG,CAAC,uBAAuB,EAAE,iBAAiB,KAAK,CAAC;QAC9D,SAAS,IAAI,CAAC,WAAW,CAAC;QAC1B,EAAE,KAAK;QACP,SAAS,IAAI,CAAC,WAAW,CAAC;QAC1B,IAAI,eAAe,CAAC;QACpB,MAAM;YAAC,UAAU;YAAqB,gBAAgB;QAA8B;IACtF;IAEA,MAAM,wBAAwB;QAC5B,gBAAgB;IAClB;IAEA,MAAM,uBAAuB,0HAAA,CAAA,wBAAqB,CAAC,IAAI,CAAC,CAAA,OAAQ,KAAK,KAAK,KAAK;IAC/E,MAAM,wBAAwB,uBAAuB,EAAE,qBAAqB,QAAQ,IAAI;IAExF,qBACE,8OAAC;QAAI,WAAU;;0BACZ,8OAAC;;kCACA,8OAAC,iIAAA,CAAA,QAAK;wBAAC,SAAQ;wBAA6B,WAAU;;0CACpD,8OAAC,4MAAA,CAAA,YAAS;gCAAC,WAAU;;;;;;4BACpB,EAAE;;;;;;;kCAEL,8OAAC,kIAAA,CAAA,SAAM;wBAAC,OAAO;wBAAkB,eAAe;;0CAC9C,8OAAC,kIAAA,CAAA,gBAAa;gCAAC,IAAG;gCAA6B,WAAU;0CACvD,cAAA,8OAAC,kIAAA,CAAA,cAAW;oCAAC,aAAa,EAAE;;;;;;;;;;;0CAE9B,8OAAC,kIAAA,CAAA,gBAAa;0CACX,0HAAA,CAAA,wBAAqB,CAAC,GAAG,CAAC,CAAC,qBAC1B,8OAAC,kIAAA,CAAA,aAAU;wCAAkB,OAAO,KAAK,KAAK;kDAC3C,EAAE,KAAK,QAAQ;uCADD,KAAK,KAAK;;;;;;;;;;;;;;;;kCAMhC,8OAAC;wBAAE,WAAU;kCAA2C,EAAE;;;;;;;;;;;;0BAG7D,8OAAC;;kCACC,8OAAC,iIAAA,CAAA,QAAK;wBAAC,WAAU;;0CACb,8OAAC,kNAAA,CAAA,aAAU;gCAAC,WAAU;;;;;;4BACrB,EAAE;;;;;;;kCAEP,8OAAC;wBAAE,WAAU;kCAA2C,EAAE;;;;;;kCAE1D,8OAAC;wBAAK,UAAU;wBAAe,WAAU;;0CACvC,8OAAC,iIAAA,CAAA,QAAK;gCACJ,MAAK;gCACL,OAAO;gCACP,UAAU,CAAC,IAAM,WAAW,EAAE,MAAM,CAAC,KAAK;gCAC1C,aAAa,EAAE;gCACf,WAAU;gCACV,cAAY,EAAE;;;;;;0CAEhB,8OAAC,kIAAA,CAAA,SAAM;gCAAC,MAAK;gCAAS,MAAK;gCAAO,SAAQ;gCAAU,cAAY,EAAE;0CAChE,cAAA,8OAAC,kNAAA,CAAA,aAAU;oCAAC,WAAU;;;;;;;;;;;;;;;;;oBAIzB,qBAAqB,MAAM,GAAG,kBAC7B,8OAAC,0IAAA,CAAA,aAAU;wBAAC,WAAU;kCACpB,cAAA,8OAAC;4BAAG,WAAU;sCACX,qBAAqB,GAAG,CAAC,CAAC,qBACzB,8OAAC;oCAAc,WAAU;;sDACvB,8OAAC;4CAAK,WAAU;sDAAW;;;;;;sDAC3B,8OAAC,kIAAA,CAAA,SAAM;4CAAC,SAAQ;4CAAQ,MAAK;4CAAO,WAAU;4CAAU,SAAS,IAAM,WAAW,MAAM;4CAAmB,cAAY,EAAE,wBAAwB;gDAAE;4CAAK;sDACtJ,cAAA,8OAAC,4MAAA,CAAA,UAAO;gDAAC,WAAU;;;;;;;;;;;;mCAHd;;;;;;;;;;;;;;6CAUf,8OAAC;wBAAE,WAAU;kCAAgF,EAAE;;;;;;;;;;;;0BAInG,8OAAC;gBAAI,WAAU;;kCACZ,8OAAC,iIAAA,CAAA,QAAK;wBAAC,WAAU;;0CACd,8OAAC,8MAAA,CAAA,WAAQ;gCAAC,WAAU;;;;;;4BACnB,EAAE;;;;;;;kCAEP,8OAAC;wBAAI,WAAU;;0CACX,8OAAC,kIAAA,CAAA,SAAM;gCAAC,SAAQ;gCAAU,SAAS,IAAM,aAAa,OAAO,EAAE;;kDAC3D,8OAAC,sMAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;oCAAiB;oCAAE,EAAE;;;;;;;0CAE3C,8OAAC;gCAAM,MAAK;gCAAO,QAAO;gCAAQ,KAAK;gCAAc,UAAU;gCAAc,WAAU;;;;;;0CAEvF,8OAAC,kIAAA,CAAA,SAAM;gCAAC,SAAQ;gCAAU,SAAS;gCAAc,UAAU,qBAAqB,MAAM,KAAK;;kDACvF,8OAAC,0MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;oCAAiB;oCAAE,EAAE;;;;;;;;;;;;;kCAGhD,8OAAC;wBAAE,WAAU;kCAA2C,EAAE;;;;;;;;;;;;0BAG7D,8OAAC;;kCACC,8OAAC,2IAAA,CAAA,cAAW;;0CACV,8OAAC,2IAAA,CAAA,qBAAkB;gCAAC,OAAO;0CACzB,cAAA,8OAAC,kIAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAc,WAAU;oCAAS,UAAU,qBAAqB,MAAM,KAAK;;sDACzF,8OAAC,0MAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;wCAAiB;wCAAE,EAAE,oCAAoC;4CAAE,UAAU;wCAAsB;;;;;;;;;;;;0CAGjH,8OAAC,2IAAA,CAAA,qBAAkB;;kDACjB,8OAAC,2IAAA,CAAA,oBAAiB;;0DAChB,8OAAC,2IAAA,CAAA,mBAAgB;0DAAE,EAAE;;;;;;0DACrB,8OAAC,2IAAA,CAAA,yBAAsB;0DACpB,EAAE,gDAAgD;oDAAE,UAAU;gDAAsB;;;;;;;;;;;;kDAGzF,8OAAC,2IAAA,CAAA,oBAAiB;;0DAChB,8OAAC,2IAAA,CAAA,oBAAiB;0DAAE,EAAE;;;;;;0DACtB,8OAAC,2IAAA,CAAA,oBAAiB;gDAAC,SAAS;gDAAuB,WAAU;0DAC1D,EAAE;;;;;;;;;;;;;;;;;;;;;;;;kCAKX,8OAAC;wBAAE,WAAU;kCAAuD,EAAE;;;;;;;;;;;;;;;;;;AAI9E", "debugId": null}}, {"offset": {"line": 2729, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/src/components/settings/feature-settings.tsx"], "sourcesContent": ["\n'use client';\n\nimport { Switch } from '@/components/ui/switch';\nimport { Label } from '@/components/ui/label';\nimport { useFeatureSettings, type FeatureSettings } from '@/contexts/feature-settings-context';\nimport { useI18n } from '@/contexts/i18n-context';\n\ninterface FeatureToggleProps {\n  featureKey: keyof FeatureSettings;\n  labelKey: string;\n  descriptionKey: string;\n}\n\nfunction FeatureToggle({ featureKey, labelKey, descriptionKey }: FeatureToggleProps) {\n  const { settings, setFeatureSetting } = useFeatureSettings();\n  const { t } = useI18n();\n\n  const isChecked = settings[featureKey];\n  const handleToggle = (checked: boolean) => {\n    setFeatureSetting(featureKey, checked);\n  };\n\n  return (\n    <div className=\"flex items-center justify-between rounded-lg border p-3 shadow-sm\">\n      <div className=\"space-y-0.5\">\n        <Label htmlFor={featureKey} className=\"font-medium\">\n          {t(labelKey)}\n        </Label>\n        <p className=\"text-xs text-muted-foreground text-start\">\n          {t(descriptionKey)}\n        </p>\n      </div>\n      <Switch\n        id={featureKey}\n        checked={isChecked}\n        onCheckedChange={handleToggle}\n        aria-label={t(labelKey)}\n      />\n    </div>\n  );\n}\n\nfunction FeatureSection({ titleKey, children }: { titleKey: string; children: React.ReactNode }) {\n    const { t } = useI18n();\n    return (\n        <div>\n            <h4 className=\"mb-4 text-lg font-semibold leading-none tracking-tight\">{t(titleKey)} ✨</h4>\n            <div className=\"space-y-3\">\n                {children}\n            </div>\n        </div>\n    );\n}\n\nexport function FeatureSettings() {\n  const {t} = useI18n();\n  return (\n    <div className=\"space-y-8\">\n       <FeatureSection titleKey=\"generativeAiFeaturesLabel\">\n            <FeatureToggle \n                featureKey=\"showAiSuggestionsOnSelection\"\n                labelKey=\"showAiSuggestionsOnTextSelectionLabel\"\n                descriptionKey=\"showAiSuggestionsOnTextSelectionDescription\"\n            />\n             <FeatureToggle \n                featureKey=\"quickAiActions\"\n                labelKey=\"quickAiActionsForSelectedTextLabel\"\n                descriptionKey=\"quickAiActionsForSelectedTextDescription\"\n            />\n            <FeatureToggle \n                featureKey=\"aiQuickReplies\"\n                labelKey=\"aiQuickReplySuggestionsLabel\"\n                descriptionKey=\"aiQuickReplySuggestionsDescription\"\n            />\n            <FeatureToggle \n                featureKey=\"promptHistory\"\n                labelKey=\"viewAndReuseRecentPromptHistoryLabel\"\n                descriptionKey=\"viewAndReuseRecentPromptHistoryDescription\"\n            />\n       </FeatureSection>\n       \n       <FeatureSection titleKey=\"autoCorrectionFeaturesLabel\">\n            <FeatureToggle \n                featureKey=\"autoCorrect\"\n                labelKey=\"automaticTextCorrectionLabel\"\n                descriptionKey=\"automaticTextCorrectionDescription\"\n            />\n            <FeatureToggle \n                featureKey=\"realTimeCorrection\"\n                labelKey=\"realTimeCorrectionLabel\"\n                descriptionKey=\"realTimeCorrectionDescription\"\n            />\n            <FeatureToggle \n                featureKey=\"sentenceEnhancement\"\n                labelKey=\"sentenceEnhancementLabel\"\n                descriptionKey=\"sentenceEnhancementDescription\"\n            />\n            <FeatureToggle \n                featureKey=\"showCorrectionFeedback\"\n                labelKey=\"showCorrectionFeedbackLabel\"\n                descriptionKey=\"showCorrectionFeedbackDescription\"\n            />\n       </FeatureSection>\n\n       <FeatureSection titleKey=\"coreCheckingFeaturesLabel\">\n         <FeatureToggle \n              featureKey=\"realTimeGrammarCheck\"\n              labelKey=\"realTimeGrammarCheckingLabel\"\n              descriptionKey=\"realTimeGrammarCheckingDescription\"\n          />\n          <FeatureToggle \n              featureKey=\"realTimeSpellCheck\"\n              labelKey=\"realTimeSpellCheckingLabel\"\n              descriptionKey=\"realTimeSpellCheckingDescription\"\n          />\n          <FeatureToggle \n              featureKey=\"styleSuggestions\"\n              labelKey=\"styleSuggestionsLabel\"\n              descriptionKey=\"styleSuggestionsDescription\"\n          />\n       </FeatureSection>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AACA;AALA;;;;;;AAaA,SAAS,cAAc,EAAE,UAAU,EAAE,QAAQ,EAAE,cAAc,EAAsB;IACjF,MAAM,EAAE,QAAQ,EAAE,iBAAiB,EAAE,GAAG,CAAA,GAAA,kJAAA,CAAA,qBAAkB,AAAD;IACzD,MAAM,EAAE,CAAC,EAAE,GAAG,CAAA,GAAA,mIAAA,CAAA,UAAO,AAAD;IAEpB,MAAM,YAAY,QAAQ,CAAC,WAAW;IACtC,MAAM,eAAe,CAAC;QACpB,kBAAkB,YAAY;IAChC;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,iIAAA,CAAA,QAAK;wBAAC,SAAS;wBAAY,WAAU;kCACnC,EAAE;;;;;;kCAEL,8OAAC;wBAA<PERSON>,WAAU;kCACV,EAAE;;;;;;;;;;;;0BAGP,8OAAC,kIAAA,CAAA,SAAM;gBACL,IAAI;gBACJ,SAAS;gBACT,iBAAiB;gBACjB,cAAY,EAAE;;;;;;;;;;;;AAItB;AAEA,SAAS,eAAe,EAAE,QAAQ,EAAE,QAAQ,EAAmD;IAC3F,MAAM,EAAE,CAAC,EAAE,GAAG,CAAA,GAAA,mIAAA,CAAA,UAAO,AAAD;IACpB,qBACI,8OAAC;;0BACG,8OAAC;gBAAG,WAAU;;oBAA0D,EAAE;oBAAU;;;;;;;0BACpF,8OAAC;gBAAI,WAAU;0BACV;;;;;;;;;;;;AAIjB;AAEO,SAAS;IACd,MAAM,EAAC,CAAC,EAAC,GAAG,CAAA,GAAA,mIAAA,CAAA,UAAO,AAAD;IAClB,qBACE,8OAAC;QAAI,WAAU;;0BACZ,8OAAC;gBAAe,UAAS;;kCACpB,8OAAC;wBACG,YAAW;wBACX,UAAS;wBACT,gBAAe;;;;;;kCAElB,8OAAC;wBACE,YAAW;wBACX,UAAS;wBACT,gBAAe;;;;;;kCAEnB,8OAAC;wBACG,YAAW;wBACX,UAAS;wBACT,gBAAe;;;;;;kCAEnB,8OAAC;wBACG,YAAW;wBACX,UAAS;wBACT,gBAAe;;;;;;;;;;;;0BAIxB,8OAAC;gBAAe,UAAS;;kCACpB,8OAAC;wBACG,YAAW;wBACX,UAAS;wBACT,gBAAe;;;;;;kCAEnB,8OAAC;wBACG,YAAW;wBACX,UAAS;wBACT,gBAAe;;;;;;kCAEnB,8OAAC;wBACG,YAAW;wBACX,UAAS;wBACT,gBAAe;;;;;;kCAEnB,8OAAC;wBACG,YAAW;wBACX,UAAS;wBACT,gBAAe;;;;;;;;;;;;0BAIxB,8OAAC;gBAAe,UAAS;;kCACvB,8OAAC;wBACI,YAAW;wBACX,UAAS;wBACT,gBAAe;;;;;;kCAEnB,8OAAC;wBACG,YAAW;wBACX,UAAS;wBACT,gBAAe;;;;;;kCAEnB,8OAAC;wBACG,YAAW;wBACX,UAAS;wBACT,gBAAe;;;;;;;;;;;;;;;;;;AAK7B", "debugId": null}}, {"offset": {"line": 2970, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/src/components/settings/writing-aid-settings.tsx"], "sourcesContent": ["\n'use client';\n\nimport { Switch } from '@/components/ui/switch';\nimport { Label } from '@/components/ui/label';\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from \"@/components/ui/select\";\nimport { useFeatureSettings, type FeatureSettings } from '@/contexts/feature-settings-context';\nimport { useI18n } from '@/contexts/i18n-context';\nimport { PROFICIENCY_LEVELS, APP_WRITING_LANGUAGES, type ProficiencyLevel } from '@/config/languages';\nimport { useMemo } from 'react';\nimport { MicVocal, SearchCheck, Vote, Users } from 'lucide-react';\n\ninterface FeatureToggleProps {\n  featureKey: keyof FeatureSettings;\n  titleKey: string;\n  descriptionKey: string;\n  icon: React.ReactNode;\n}\n\nfunction FeatureToggle({ featureKey, titleKey, descriptionKey, icon }: FeatureToggleProps) {\n  const { settings, setFeatureSetting } = useFeatureSettings();\n  const { t } = useI18n();\n\n  const isChecked = !!settings[featureKey];\n  const handleToggle = (checked: boolean) => {\n    setFeatureSetting(featureKey, checked);\n  };\n\n  return (\n    <div className=\"p-4 rounded-lg border bg-muted/30\">\n      <div className=\"flex items-start justify-between gap-4\">\n        <div className=\"flex items-start gap-3\">\n          <div className=\"text-primary mt-1\">{icon}</div>\n          <div className=\"space-y-1\">\n            <Label htmlFor={featureKey as string} className=\"font-semibold text-base\">\n              {t(titleKey)}\n            </Label>\n            <p className=\"text-sm text-muted-foreground text-start\">\n              {t(descriptionKey)}\n            </p>\n          </div>\n        </div>\n        <Switch\n          id={featureKey as string}\n          checked={isChecked}\n          onCheckedChange={handleToggle}\n          aria-label={t(titleKey)}\n          className=\"shrink-0 mt-1\"\n        />\n      </div>\n    </div>\n  );\n}\n\n\nexport function WritingAidSettings() {\n    const { t, getWritingLanguageBase, languageProficiency, setLanguageProficiency } = useI18n();\n    const currentWritingBaseLang = getWritingLanguageBase();\n\n    const handleProficiencyChange = (newProficiency: ProficiencyLevel) => {\n        setLanguageProficiency(currentWritingBaseLang, newProficiency);\n    };\n\n    const currentWritingLanguageInfo = useMemo(() => {\n        return APP_WRITING_LANGUAGES.find(lang => lang.value === currentWritingBaseLang);\n    }, [currentWritingBaseLang]);\n\n    const currentProficiency = languageProficiency[currentWritingBaseLang] || 'none';\n\n    return (\n        <div className=\"space-y-6 p-1\">\n            <div className=\"space-y-1\">\n                <h3 className=\"text-lg font-semibold\">{t('writingAssistanceTitle')}</h3>\n                <p className=\"text-sm text-muted-foreground text-start\">{t('writingAssistanceDescription')}</p>\n            </div>\n\n            {currentWritingLanguageInfo?.supportsProficiency && (\n                <div className=\"p-4 rounded-lg border bg-muted/30 space-y-2\">\n                    <Label htmlFor=\"language-proficiency-select\" className=\"flex items-center text-base font-semibold\">\n                        <MicVocal className=\"mr-2 h-5 w-5 text-primary\" />\n                        {t('yourLanguageProficiencyTitle')}\n                    </Label>\n                    <p className=\"text-sm text-muted-foreground pb-2 text-start\">\n                        {t('yourLanguageProficiencyDescription')}\n                    </p>\n                    <Select value={currentProficiency} onValueChange={(value) => handleProficiencyChange(value as ProficiencyLevel)}>\n                        <SelectTrigger id=\"language-proficiency-select\" className=\"w-full bg-background\">\n                        <SelectValue placeholder={t('selectProficiencyPlaceholder')} />\n                        </SelectTrigger>\n                        <SelectContent>\n                        {PROFICIENCY_LEVELS.map((level) => (\n                            <SelectItem key={level.value} value={level.value}>\n                            {t(level.labelKey)}\n                            </SelectItem>\n                        ))}\n                        </SelectContent>\n                    </Select>\n                </div>\n            )}\n            \n            <div className=\"space-y-4\">\n                <FeatureToggle\n                    featureKey=\"enableToneDetection\"\n                    titleKey=\"toneDetectionTitle\"\n                    descriptionKey=\"toneDetectionDescription\"\n                    icon={<Vote className=\"h-5 w-5\" />}\n                />\n                <FeatureToggle\n                    featureKey=\"enablePlagiarismDetection\"\n                    titleKey=\"plagiarismDetectionSettingsTitle\"\n                    descriptionKey=\"plagiarismDetectionDescription\"\n                    icon={<SearchCheck className=\"h-5 w-5\" />}\n                />\n                <FeatureToggle\n                    featureKey=\"enableNonNativeSpeakerSupport\"\n                    titleKey=\"nonNativeSupportTitle\"\n                    descriptionKey=\"nonNativeSupportDescription\"\n                    icon={<Users className=\"h-5 w-5\" />}\n                />\n            </div>\n        </div>\n    );\n}\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AATA;;;;;;;;;;AAkBA,SAAS,cAAc,EAAE,UAAU,EAAE,QAAQ,EAAE,cAAc,EAAE,IAAI,EAAsB;IACvF,MAAM,EAAE,QAAQ,EAAE,iBAAiB,EAAE,GAAG,CAAA,GAAA,kJAAA,CAAA,qBAAkB,AAAD;IACzD,MAAM,EAAE,CAAC,EAAE,GAAG,CAAA,GAAA,mIAAA,CAAA,UAAO,AAAD;IAEpB,MAAM,YAAY,CAAC,CAAC,QAAQ,CAAC,WAAW;IACxC,MAAM,eAAe,CAAC;QACpB,kBAAkB,YAAY;IAChC;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCAAqB;;;;;;sCACpC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,iIAAA,CAAA,QAAK;oCAAC,SAAS;oCAAsB,WAAU;8CAC7C,EAAE;;;;;;8CAEL,8OAAC;oCAAE,WAAU;8CACV,EAAE;;;;;;;;;;;;;;;;;;8BAIT,8OAAC,kIAAA,CAAA,SAAM;oBACL,IAAI;oBACJ,SAAS;oBACT,iBAAiB;oBACjB,cAAY,EAAE;oBACd,WAAU;;;;;;;;;;;;;;;;;AAKpB;AAGO,SAAS;IACZ,MAAM,EAAE,CAAC,EAAE,sBAAsB,EAAE,mBAAmB,EAAE,sBAAsB,EAAE,GAAG,CAAA,GAAA,mIAAA,CAAA,UAAO,AAAD;IACzF,MAAM,yBAAyB;IAE/B,MAAM,0BAA0B,CAAC;QAC7B,uBAAuB,wBAAwB;IACnD;IAEA,MAAM,6BAA6B,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QACvC,OAAO,0HAAA,CAAA,wBAAqB,CAAC,IAAI,CAAC,CAAA,OAAQ,KAAK,KAAK,KAAK;IAC7D,GAAG;QAAC;KAAuB;IAE3B,MAAM,qBAAqB,mBAAmB,CAAC,uBAAuB,IAAI;IAE1E,qBACI,8OAAC;QAAI,WAAU;;0BACX,8OAAC;gBAAI,WAAU;;kCACX,8OAAC;wBAAG,WAAU;kCAAyB,EAAE;;;;;;kCACzC,8OAAC;wBAAE,WAAU;kCAA4C,EAAE;;;;;;;;;;;;YAG9D,4BAA4B,qCACzB,8OAAC;gBAAI,WAAU;;kCACX,8OAAC,iIAAA,CAAA,QAAK;wBAAC,SAAQ;wBAA8B,WAAU;;0CACnD,8OAAC,8MAAA,CAAA,WAAQ;gCAAC,WAAU;;;;;;4BACnB,EAAE;;;;;;;kCAEP,8OAAC;wBAAE,WAAU;kCACR,EAAE;;;;;;kCAEP,8OAAC,kIAAA,CAAA,SAAM;wBAAC,OAAO;wBAAoB,eAAe,CAAC,QAAU,wBAAwB;;0CACjF,8OAAC,kIAAA,CAAA,gBAAa;gCAAC,IAAG;gCAA8B,WAAU;0CAC1D,cAAA,8OAAC,kIAAA,CAAA,cAAW;oCAAC,aAAa,EAAE;;;;;;;;;;;0CAE5B,8OAAC,kIAAA,CAAA,gBAAa;0CACb,0HAAA,CAAA,qBAAkB,CAAC,GAAG,CAAC,CAAC,sBACrB,8OAAC,kIAAA,CAAA,aAAU;wCAAmB,OAAO,MAAM,KAAK;kDAC/C,EAAE,MAAM,QAAQ;uCADA,MAAM,KAAK;;;;;;;;;;;;;;;;;;;;;;0BAS5C,8OAAC;gBAAI,WAAU;;kCACX,8OAAC;wBACG,YAAW;wBACX,UAAS;wBACT,gBAAe;wBACf,oBAAM,8OAAC,kMAAA,CAAA,OAAI;4BAAC,WAAU;;;;;;;;;;;kCAE1B,8OAAC;wBACG,YAAW;wBACX,UAAS;wBACT,gBAAe;wBACf,oBAAM,8OAAC,oNAAA,CAAA,cAAW;4BAAC,WAAU;;;;;;;;;;;kCAEjC,8OAAC;wBACG,YAAW;wBACX,UAAS;wBACT,gBAAe;wBACf,oBAAM,8OAAC,oMAAA,CAAA,QAAK;4BAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;AAK3C", "debugId": null}}, {"offset": {"line": 3257, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/src/components/settings/advanced-settings.tsx"], "sourcesContent": ["\n'use client';\n\nimport { Switch } from '@/components/ui/switch';\nimport { Label } from '@/components/ui/label';\nimport { useFeatureSettings, type FeatureSettings } from '@/contexts/feature-settings-context';\nimport { useI18n } from '@/contexts/i18n-context';\nimport { Button } from '../ui/button';\nimport { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from '../ui/alert-dialog';\nimport { Trash2 } from 'lucide-react';\nimport { useToast } from '@/hooks/use-toast';\nimport { Separator } from '../ui/separator';\n\nfunction AdvancedFeatureToggle({ featureKey, labelKey, descriptionKey }: { featureKey: keyof FeatureSettings, labelKey: string, descriptionKey: string }) {\n  const { settings, setFeatureSetting } = useFeatureSettings();\n  const { t } = useI18n();\n\n  const isChecked = !!settings[featureKey];\n  const handleToggle = (checked: boolean) => {\n    setFeatureSetting(featureKey, checked);\n  };\n\n  return (\n    <div className=\"flex items-center justify-between rounded-lg border p-3 shadow-sm\">\n      <div className=\"space-y-0.5\">\n        <Label htmlFor={featureKey as string} className=\"font-medium\">\n          {t(labelKey)}\n        </Label>\n        <p className=\"text-xs text-muted-foreground\">\n          {t(descriptionKey)}\n        </p>\n      </div>\n      <Switch\n        id={featureKey as string}\n        checked={isChecked}\n        onCheckedChange={handleToggle}\n        aria-label={t(labelKey)}\n      />\n    </div>\n  );\n}\n\n\nexport function AdvancedSettings() {\n    const { t } = useI18n();\n    const { toast } = useToast();\n\n    const handleResetSettings = () => {\n        // Clear all known settings from localStorage\n        localStorage.removeItem('lingua-flow-feature-settings');\n        localStorage.removeItem('lingua-flow-theme');\n        localStorage.removeItem('lingua-flow-font-size');\n        localStorage.removeItem('lingua-flow-high-contrast');\n        localStorage.removeItem('lingua-flow-ui-language');\n        localStorage.removeItem('lingua-flow-writing-language-dialect');\n        localStorage.removeItem('lingua-flow-language-proficiency');\n        localStorage.removeItem('lingua-flow-dictionary');\n        \n        toast({ titleKey: \"toastSuccessTitle\", descriptionKey: \"toastResetSuccess\" });\n\n        // Reload the page to apply defaults\n        setTimeout(() => {\n            window.location.reload();\n        }, 1000);\n    };\n\n    return (\n        <div className=\"space-y-6\">\n            <div className=\"space-y-4\">\n                 <AdvancedFeatureToggle\n                    featureKey=\"enableAutomaticLanguageDetection\"\n                    labelKey=\"enableAutomaticLanguageDetectionLabel\"\n                    descriptionKey=\"enableAutomaticLanguageDetectionDescription\"\n                />\n                <AdvancedFeatureToggle\n                    featureKey=\"enableOfflineFunctionality\"\n                    labelKey=\"enableOfflineFunctionalityLabel\"\n                    descriptionKey=\"enableOfflineFunctionalityDescription\"\n                />\n            </div>\n\n            <Separator />\n            \n            <div className=\"space-y-3\">\n                <h3 className=\"text-lg font-semibold leading-none tracking-tight\">{t('dataManagementLabel')}</h3>\n                <div className=\"rounded-lg border border-destructive/50 p-4\">\n                    <div className=\"flex items-center justify-between gap-4\">\n                         <div className=\"space-y-1\">\n                            <Label className=\"font-medium text-destructive\">{t('resetAllSettingsLabel')}</Label>\n                            <p className=\"text-xs text-muted-foreground\">{t('resetAllSettingsDescription')}</p>\n                         </div>\n                         <AlertDialog>\n                            <AlertDialogTrigger asChild>\n                                <Button variant=\"destructive\" className=\"shrink-0\">\n                                    <Trash2 className=\"mr-2 h-4 w-4\" /> {t('resetButtonLabel')}\n                                </Button>\n                            </AlertDialogTrigger>\n                            <AlertDialogContent>\n                                <AlertDialogHeader>\n                                <AlertDialogTitle>{t('resetAllSettingsConfirmTitle')}</AlertDialogTitle>\n                                <AlertDialogDescription>\n                                    {t('resetAllSettingsConfirmDescription')}\n                                </AlertDialogDescription>\n                                </AlertDialogHeader>\n                                <AlertDialogFooter>\n                                <AlertDialogCancel>{t('cancelButton')}</AlertDialogCancel>\n                                <AlertDialogAction onClick={handleResetSettings} className=\"bg-destructive hover:bg-destructive/90\">\n                                    {t('confirmResetButton')}\n                                </AlertDialogAction>\n                                </AlertDialogFooter>\n                            </AlertDialogContent>\n                        </AlertDialog>\n                    </div>\n                </div>\n            </div>\n        </div>\n    );\n}\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAVA;;;;;;;;;;;AAYA,SAAS,sBAAsB,EAAE,UAAU,EAAE,QAAQ,EAAE,cAAc,EAAmF;IACtJ,MAAM,EAAE,QAAQ,EAAE,iBAAiB,EAAE,GAAG,CAAA,GAAA,kJAAA,CAAA,qBAAkB,AAAD;IACzD,MAAM,EAAE,CAAC,EAAE,GAAG,CAAA,GAAA,mIAAA,CAAA,UAAO,AAAD;IAEpB,MAAM,YAAY,CAAC,CAAC,QAAQ,CAAC,WAAW;IACxC,MAAM,eAAe,CAAC;QACpB,kBAAkB,YAAY;IAChC;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,iIAAA,CAAA,QAAK;wBAAC,SAAS;wBAAsB,WAAU;kCAC7C,EAAE;;;;;;kCAEL,8OAAC;wBAAE,WAAU;kCACV,EAAE;;;;;;;;;;;;0BAGP,8OAAC,kIAAA,CAAA,SAAM;gBACL,IAAI;gBACJ,SAAS;gBACT,iBAAiB;gBACjB,cAAY,EAAE;;;;;;;;;;;;AAItB;AAGO,SAAS;IACZ,MAAM,EAAE,CAAC,EAAE,GAAG,CAAA,GAAA,mIAAA,CAAA,UAAO,AAAD;IACpB,MAAM,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,4HAAA,CAAA,WAAQ,AAAD;IAEzB,MAAM,sBAAsB;QACxB,6CAA6C;QAC7C,aAAa,UAAU,CAAC;QACxB,aAAa,UAAU,CAAC;QACxB,aAAa,UAAU,CAAC;QACxB,aAAa,UAAU,CAAC;QACxB,aAAa,UAAU,CAAC;QACxB,aAAa,UAAU,CAAC;QACxB,aAAa,UAAU,CAAC;QACxB,aAAa,UAAU,CAAC;QAExB,MAAM;YAAE,UAAU;YAAqB,gBAAgB;QAAoB;QAE3E,oCAAoC;QACpC,WAAW;YACP,OAAO,QAAQ,CAAC,MAAM;QAC1B,GAAG;IACP;IAEA,qBACI,8OAAC;QAAI,WAAU;;0BACX,8OAAC;gBAAI,WAAU;;kCACV,8OAAC;wBACE,YAAW;wBACX,UAAS;wBACT,gBAAe;;;;;;kCAEnB,8OAAC;wBACG,YAAW;wBACX,UAAS;wBACT,gBAAe;;;;;;;;;;;;0BAIvB,8OAAC,qIAAA,CAAA,YAAS;;;;;0BAEV,8OAAC;gBAAI,WAAU;;kCACX,8OAAC;wBAAG,WAAU;kCAAqD,EAAE;;;;;;kCACrE,8OAAC;wBAAI,WAAU;kCACX,cAAA,8OAAC;4BAAI,WAAU;;8CACV,8OAAC;oCAAI,WAAU;;sDACZ,8OAAC,iIAAA,CAAA,QAAK;4CAAC,WAAU;sDAAgC,EAAE;;;;;;sDACnD,8OAAC;4CAAE,WAAU;sDAAiC,EAAE;;;;;;;;;;;;8CAEnD,8OAAC,2IAAA,CAAA,cAAW;;sDACT,8OAAC,2IAAA,CAAA,qBAAkB;4CAAC,OAAO;sDACvB,cAAA,8OAAC,kIAAA,CAAA,SAAM;gDAAC,SAAQ;gDAAc,WAAU;;kEACpC,8OAAC,0MAAA,CAAA,SAAM;wDAAC,WAAU;;;;;;oDAAiB;oDAAE,EAAE;;;;;;;;;;;;sDAG/C,8OAAC,2IAAA,CAAA,qBAAkB;;8DACf,8OAAC,2IAAA,CAAA,oBAAiB;;sEAClB,8OAAC,2IAAA,CAAA,mBAAgB;sEAAE,EAAE;;;;;;sEACrB,8OAAC,2IAAA,CAAA,yBAAsB;sEAClB,EAAE;;;;;;;;;;;;8DAGP,8OAAC,2IAAA,CAAA,oBAAiB;;sEAClB,8OAAC,2IAAA,CAAA,oBAAiB;sEAAE,EAAE;;;;;;sEACtB,8OAAC,2IAAA,CAAA,oBAAiB;4DAAC,SAAS;4DAAqB,WAAU;sEACtD,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUvC", "debugId": null}}, {"offset": {"line": 3550, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/src/components/settings/settings-panel.tsx"], "sourcesContent": ["\n\"use client\";\n\nimport { useState } from \"react\";\nimport { Settings2, Languages, Palette, BookMarked, SlidersHorizontal, PenTool, Cog } from \"lucide-react\"; \nimport { useI18n } from \"@/contexts/i18n-context\";\nimport { LanguageSettings } from \"./language-settings\";\nimport { AppearanceSettings } from \"./appearance-settings\";\nimport { DictionarySettings } from \"./dictionary-settings\";\nimport { ScrollArea } from \"@/components/ui/scroll-area\";\nimport { FeatureSettings } from \"./feature-settings\";\nimport { WritingAidSettings } from \"./writing-aid-settings\";\nimport { AdvancedSettings } from \"./advanced-settings\";\nimport { cn } from \"@/lib/utils\";\nimport { Button } from \"@/components/ui/button\";\n\nexport function SettingsPanel() {\n  const { t } = useI18n(); \n\n  const settingsCategories = [\n    { id: 'language', labelKey: 'languageLabel', Icon: Languages, component: <LanguageSettings /> },\n    { id: 'appearance', labelKey: 'appearanceLabel', Icon: Palette, component: <AppearanceSettings /> },\n    { id: 'dictionary', labelKey: 'dictionaryLabel', Icon: BookMarked, component: <DictionarySettings /> },\n    { id: 'features', labelKey: 'featuresLabel', Icon: SlidersHorizontal, component: <FeatureSettings /> },\n    { id: 'writing-aid', labelKey: 'writingAidLabel', Icon: PenTool, component: <WritingAidSettings /> },\n    { id: 'advanced', labelKey: 'advancedSettingsLabel', Icon: Cog, component: <AdvancedSettings /> },\n  ];\n\n  const [activeCategory, setActiveCategory] = useState(settingsCategories[0].id);\n\n  const activeComponent = settingsCategories.find(c => c.id === activeCategory)?.component;\n\n  return (\n    <div className=\"w-full bg-background text-foreground flex flex-col h-full p-4 md:p-6\">\n      <div className=\"pb-4 border-b shrink-0\">\n        <h3 className=\"flex items-center text-2xl font-semibold\">\n          <Settings2 className=\"mr-3 h-6 w-6\" />\n          {t('settingsTitle')}\n        </h3>\n        <p className=\"text-sm text-muted-foreground mt-1\">\n          {t('settingsDescription')}\n        </p>\n      </div>\n      \n      <div className=\"flex flex-col md:flex-row flex-grow min-h-0 pt-6 gap-6 md:gap-8\">\n        {/* Navigation Panel */}\n        <div className=\"w-full md:w-[16rem] shrink-0\">\n          <nav className=\"flex flex-col gap-1\">\n            {settingsCategories.map(({ id, labelKey, Icon }) => (\n              <Button\n                key={id}\n                variant=\"ghost\"\n                onClick={() => setActiveCategory(id)}\n                className={cn(\n                  \"w-full justify-start text-left h-auto py-2 px-3 text-base md:text-sm\",\n                  activeCategory === id && \"bg-accent text-accent-foreground\"\n                )}\n              >\n                <Icon className=\"mr-2.5 h-4 w-4 shrink-0\" />\n                <span className=\"truncate\">{t(labelKey)}</span>\n              </Button>\n            ))}\n          </nav>\n        </div>\n\n        {/* Content Panel */}\n        <div className=\"flex-1 relative md:border-l md:pl-8\">\n            <div className=\"absolute inset-0\">\n                <ScrollArea className=\"h-full w-full\">\n                    <div className=\"pr-2\">\n                        {activeComponent}\n                    </div>\n                </ScrollArea>\n            </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAGA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAbA;;;;;;;;;;;;;;AAeO,SAAS;IACd,MAAM,EAAE,CAAC,EAAE,GAAG,CAAA,GAAA,mIAAA,CAAA,UAAO,AAAD;IAEpB,MAAM,qBAAqB;QACzB;YAAE,IAAI;YAAY,UAAU;YAAiB,MAAM,4MAAA,CAAA,YAAS;YAAE,yBAAW,8OAAC,sJAAA,CAAA,mBAAgB;;;;;QAAI;QAC9F;YAAE,IAAI;YAAc,UAAU;YAAmB,MAAM,wMAAA,CAAA,UAAO;YAAE,yBAAW,8OAAC,wJAAA,CAAA,qBAAkB;;;;;QAAI;QAClG;YAAE,IAAI;YAAc,UAAU;YAAmB,MAAM,kNAAA,CAAA,aAAU;YAAE,yBAAW,8OAAC,wJAAA,CAAA,qBAAkB;;;;;QAAI;QACrG;YAAE,IAAI;YAAY,UAAU;YAAiB,MAAM,gOAAA,CAAA,oBAAiB;YAAE,yBAAW,8OAAC,qJAAA,CAAA,kBAAe;;;;;QAAI;QACrG;YAAE,IAAI;YAAe,UAAU;YAAmB,MAAM,4MAAA,CAAA,UAAO;YAAE,yBAAW,8OAAC,4JAAA,CAAA,qBAAkB;;;;;QAAI;QACnG;YAAE,IAAI;YAAY,UAAU;YAAyB,MAAM,gMAAA,CAAA,MAAG;YAAE,yBAAW,8OAAC,sJAAA,CAAA,mBAAgB;;;;;QAAI;KACjG;IAED,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,kBAAkB,CAAC,EAAE,CAAC,EAAE;IAE7E,MAAM,kBAAkB,mBAAmB,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,iBAAiB;IAE/E,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;;0CACZ,8OAAC,gNAAA,CAAA,YAAS;gCAAC,WAAU;;;;;;4BACpB,EAAE;;;;;;;kCAEL,8OAAC;wBAAE,WAAU;kCACV,EAAE;;;;;;;;;;;;0BAIP,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;sCACZ,mBAAmB,GAAG,CAAC,CAAC,EAAE,EAAE,EAAE,QAAQ,EAAE,IAAI,EAAE,iBAC7C,8OAAC,kIAAA,CAAA,SAAM;oCAEL,SAAQ;oCACR,SAAS,IAAM,kBAAkB;oCACjC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wEACA,mBAAmB,MAAM;;sDAG3B,8OAAC;4CAAK,WAAU;;;;;;sDAChB,8OAAC;4CAAK,WAAU;sDAAY,EAAE;;;;;;;mCATzB;;;;;;;;;;;;;;;kCAgBb,8OAAC;wBAAI,WAAU;kCACX,cAAA,8OAAC;4BAAI,WAAU;sCACX,cAAA,8OAAC,0IAAA,CAAA,aAAU;gCAAC,WAAU;0CAClB,cAAA,8OAAC;oCAAI,WAAU;8CACV;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQzB", "debugId": null}}, {"offset": {"line": 3780, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/src/components/ui/accordion.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as AccordionPrimitive from \"@radix-ui/react-accordion\"\nimport { ChevronDown } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Accordion = AccordionPrimitive.Root\n\nconst AccordionItem = React.forwardRef<\n  React.ElementRef<typeof AccordionPrimitive.Item>,\n  React.ComponentPropsWithoutRef<typeof AccordionPrimitive.Item>\n>(({ className, ...props }, ref) => (\n  <AccordionPrimitive.Item\n    ref={ref}\n    className={cn(\"border-b\", className)}\n    {...props}\n  />\n))\nAccordionItem.displayName = \"AccordionItem\"\n\nconst AccordionTrigger = React.forwardRef<\n  React.ElementRef<typeof AccordionPrimitive.Trigger>,\n  React.ComponentPropsWithoutRef<typeof AccordionPrimitive.Trigger>\n>(({ className, children, ...props }, ref) => (\n  <AccordionPrimitive.Header className=\"flex\">\n    <AccordionPrimitive.Trigger\n      ref={ref}\n      className={cn(\n        \"flex flex-1 items-center justify-between py-4 font-medium transition-all hover:underline [&[data-state=open]>svg]:rotate-180\",\n        className\n      )}\n      {...props}\n    >\n      {children}\n      <ChevronDown className=\"h-4 w-4 shrink-0 transition-transform duration-200\" />\n    </AccordionPrimitive.Trigger>\n  </AccordionPrimitive.Header>\n))\nAccordionTrigger.displayName = AccordionPrimitive.Trigger.displayName\n\nconst AccordionContent = React.forwardRef<\n  React.ElementRef<typeof AccordionPrimitive.Content>,\n  React.ComponentPropsWithoutRef<typeof AccordionPrimitive.Content>\n>(({ className, children, ...props }, ref) => (\n  <AccordionPrimitive.Content\n    ref={ref}\n    className=\"overflow-hidden text-sm transition-all data-[state=closed]:animate-accordion-up data-[state=open]:animate-accordion-down\"\n    {...props}\n  >\n    <div className={cn(\"pb-4 pt-0\", className)}>{children}</div>\n  </AccordionPrimitive.Content>\n))\n\nAccordionContent.displayName = AccordionPrimitive.Content.displayName\n\nexport { Accordion, AccordionItem, AccordionTrigger, AccordionContent }\n"], "names": [], "mappings": ";;;;;;;AAEA;AACA;AACA;AAEA;AANA;;;;;;AAQA,MAAM,YAAY,qKAAA,CAAA,OAAuB;AAEzC,MAAM,8BAAgB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGnC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,qKAAA,CAAA,OAAuB;QACtB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QACzB,GAAG,KAAK;;;;;;AAGb,cAAc,WAAW,GAAG;AAE5B,MAAM,iCAAmB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGtC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,8OAAC,qKAAA,CAAA,SAAyB;QAAC,WAAU;kBACnC,cAAA,8OAAC,qKAAA,CAAA,UAA0B;YACzB,KAAK;YACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,gIACA;YAED,GAAG,KAAK;;gBAER;8BACD,8OAAC,oNAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAI7B,iBAAiB,WAAW,GAAG,qKAAA,CAAA,UAA0B,CAAC,WAAW;AAErE,MAAM,iCAAmB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGtC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,8OAAC,qKAAA,CAAA,UAA0B;QACzB,KAAK;QACL,WAAU;QACT,GAAG,KAAK;kBAET,cAAA,8OAAC;YAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,aAAa;sBAAa;;;;;;;;;;;AAIjD,iBAAiB,WAAW,GAAG,qKAAA,CAAA,UAA0B,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 3860, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/src/components/help/help-panel.tsx"], "sourcesContent": ["\n\"use client\";\n\nimport { ScrollArea } from \"@/components/ui/scroll-area\";\nimport { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from \"@/components/ui/accordion\";\nimport { \n  HelpCircle, Edit3, Wand2, Languages, Palette, BookMarked, PenTool, Cog, SlidersHorizontal\n} from \"lucide-react\";\nimport { useI18n } from \"@/contexts/i18n-context\";\n\nexport function HelpPanel() {\n  const { t } = useI18n();\n\n  const helpSections = [\n    {\n      id: \"editor\",\n      titleKey: \"helpEditorTitle\",\n      icon: <Edit3 className=\"mr-2 h-4 w-4\" />,\n      contentKey: \"helpEditorDescription\"\n    },\n    {\n      id: \"ai-tools\",\n      titleKey: \"helpAiToolsTitle\",\n      icon: <Wand2 className=\"mr-2 h-4 w-4\" />,\n      contentKey: \"helpAiToolsDescription\"\n    },\n    {\n      id: \"language-settings\",\n      titleKey: \"helpLanguageSettingsTitle\",\n      icon: <Languages className=\"mr-2 h-4 w-4\" />,\n      contentKey: \"helpLanguageSettingsDescription\"\n    },\n    {\n      id: \"appearance-settings\",\n      titleKey: \"helpAppearanceSettingsTitle\",\n      icon: <Palette className=\"mr-2 h-4 w-4\" />,\n      contentKey: \"helpAppearanceSettingsDescription\"\n    },\n    {\n      id: \"dictionary-settings\",\n      titleKey: \"helpDictionarySettingsTitle\",\n      icon: <BookMarked className=\"mr-2 h-4 w-4\" />,\n      contentKey: \"helpDictionarySettingsDescription\"\n    },\n    {\n      id: \"feature-settings\",\n      titleKey: \"helpFeatureSettingsTitle\",\n      icon: <SlidersHorizontal className=\"mr-2 h-4 w-4\" />,\n      contentKey: \"helpFeatureSettingsDescription\"\n    },\n    {\n      id: \"writing-aid-settings\",\n      titleKey: \"helpWritingAidSettingsTitle\",\n      icon: <PenTool className=\"mr-2 h-4 w-4\" />,\n      contentKey: \"helpWritingAidSettingsDescription\"\n    },\n    {\n      id: \"advanced-settings\",\n      titleKey: \"helpAdvancedSettingsTitle\",\n      icon: <Cog className=\"mr-2 h-4 w-4\" />,\n      contentKey: \"helpAdvancedSettingsDescription\"\n    },\n  ];\n\n  return (\n    <div className=\"flex flex-col h-full p-4 md:p-6\">\n      <div className=\"pb-4 border-b shrink-0\">\n        <h3 className=\"flex items-center text-2xl font-semibold text-foreground\">\n          <HelpCircle className=\"mr-3 h-6 w-6\" />\n          {t('helpPanelTitle')}\n        </h3>\n        <p className=\"text-sm text-muted-foreground mt-1 text-start\">{t('helpPanelDescription')}</p>\n      </div>\n      <ScrollArea className=\"flex-grow pt-6 -mr-4 pr-4\">\n        <div className=\"space-y-4\">\n            <p className=\"text-base text-muted-foreground leading-relaxed text-start\">{t('helpPanelIntro')}</p>\n\n            <Accordion type=\"multiple\" className=\"w-full\">\n              {helpSections.map(section => (\n                <AccordionItem value={section.id} key={section.id}>\n                  <AccordionTrigger className=\"text-lg\">\n                    <div className=\"flex items-center\">\n                      {section.icon} {t(section.titleKey)}\n                    </div>\n                  </AccordionTrigger>\n                  <AccordionContent className=\"space-y-2 py-2 pl-4 text-base text-muted-foreground leading-relaxed text-start\">\n                    <p dangerouslySetInnerHTML={{ __html: t(section.contentKey) }}></p>\n                  </AccordionContent>\n                </AccordionItem>\n              ))}\n            </Accordion>\n            \n            <p className=\"text-base text-muted-foreground pt-4 text-start\">{t('helpPanelTip')}</p>\n        </div>\n      </ScrollArea>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAGA;AAPA;;;;;;AASO,SAAS;IACd,MAAM,EAAE,CAAC,EAAE,GAAG,CAAA,GAAA,mIAAA,CAAA,UAAO,AAAD;IAEpB,MAAM,eAAe;QACnB;YACE,IAAI;YACJ,UAAU;YACV,oBAAM,8OAAC,0MAAA,CAAA,QAAK;gBAAC,WAAU;;;;;;YACvB,YAAY;QACd;QACA;YACE,IAAI;YACJ,UAAU;YACV,oBAAM,8OAAC,+MAAA,CAAA,QAAK;gBAAC,WAAU;;;;;;YACvB,YAAY;QACd;QACA;YACE,IAAI;YACJ,UAAU;YACV,oBAAM,8OAAC,4MAAA,CAAA,YAAS;gBAAC,WAAU;;;;;;YAC3B,YAAY;QACd;QACA;YACE,IAAI;YACJ,UAAU;YACV,oBAAM,8OAAC,wMAAA,CAAA,UAAO;gBAAC,WAAU;;;;;;YACzB,YAAY;QACd;QACA;YACE,IAAI;YACJ,UAAU;YACV,oBAAM,8OAAC,kNAAA,CAAA,aAAU;gBAAC,WAAU;;;;;;YAC5B,YAAY;QACd;QACA;YACE,IAAI;YACJ,UAAU;YACV,oBAAM,8OAAC,gOAAA,CAAA,oBAAiB;gBAAC,WAAU;;;;;;YACnC,YAAY;QACd;QACA;YACE,IAAI;YACJ,UAAU;YACV,oBAAM,8OAAC,4MAAA,CAAA,UAAO;gBAAC,WAAU;;;;;;YACzB,YAAY;QACd;QACA;YACE,IAAI;YACJ,UAAU;YACV,oBAAM,8OAAC,gMAAA,CAAA,MAAG;gBAAC,WAAU;;;;;;YACrB,YAAY;QACd;KACD;IAED,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;;0CACZ,8OAAC,kNAAA,CAAA,aAAU;gCAAC,WAAU;;;;;;4BACrB,EAAE;;;;;;;kCAEL,8OAAC;wBAAE,WAAU;kCAAiD,EAAE;;;;;;;;;;;;0BAElE,8OAAC,0IAAA,CAAA,aAAU;gBAAC,WAAU;0BACpB,cAAA,8OAAC;oBAAI,WAAU;;sCACX,8OAAC;4BAAE,WAAU;sCAA8D,EAAE;;;;;;sCAE7E,8OAAC,qIAAA,CAAA,YAAS;4BAAC,MAAK;4BAAW,WAAU;sCAClC,aAAa,GAAG,CAAC,CAAA,wBAChB,8OAAC,qIAAA,CAAA,gBAAa;oCAAC,OAAO,QAAQ,EAAE;;sDAC9B,8OAAC,qIAAA,CAAA,mBAAgB;4CAAC,WAAU;sDAC1B,cAAA,8OAAC;gDAAI,WAAU;;oDACZ,QAAQ,IAAI;oDAAC;oDAAE,EAAE,QAAQ,QAAQ;;;;;;;;;;;;sDAGtC,8OAAC,qIAAA,CAAA,mBAAgB;4CAAC,WAAU;sDAC1B,cAAA,8OAAC;gDAAE,yBAAyB;oDAAE,QAAQ,EAAE,QAAQ,UAAU;gDAAE;;;;;;;;;;;;mCAPzB,QAAQ,EAAE;;;;;;;;;;sCAarD,8OAAC;4BAAE,WAAU;sCAAmD,EAAE;;;;;;;;;;;;;;;;;;;;;;;AAK9E", "debugId": null}}, {"offset": {"line": 4116, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/src/components/ai/writing-mode-selector.tsx"], "sourcesContent": ["\n\"use client\";\n\nimport { Label } from \"@/components/ui/label\";\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from \"@/components/ui/select\";\nimport { Palette } from \"lucide-react\";\nimport { useI18n } from \"@/contexts/i18n-context\";\n\ninterface WritingModeSelectorProps {\n  value: string;\n  onChange: (value: string) => void;\n}\n\nconst writingModes = [\n  { value: \"Casual\", labelKey: \"casualWritingMode\" },\n  { value: \"Formal\", labelKey: \"formalWritingMode\" },\n  { value: \"Professional\", labelKey: \"professionalWritingMode\" },\n  { value: \"Creative\", labelKey: \"creativeWritingMode\" },\n  { value: \"Technical\", labelKey: \"technicalWritingMode\" },\n  { value: \"Academic\", labelKey: \"academicWritingMode\" },\n  { value: \"Business\", labelKey: \"businessWritingMode\" },\n];\n\nexport function WritingModeSelector({ value, onChange }: WritingModeSelectorProps) {\n  const { t } = useI18n();\n  return (\n    <div className=\"flex items-center gap-2\">\n      <Label htmlFor=\"writing-mode\" className=\"flex items-center text-sm font-medium text-muted-foreground\">\n        <Palette className=\"mr-2 h-3.5 w-3.5\" />\n        {t('writingModeLabel')}:\n      </Label>\n      <Select value={value} onValueChange={onChange}>\n        <SelectTrigger id=\"writing-mode\" className=\"w-full sm:w-[180px] h-9\">\n          <SelectValue placeholder={t('selectWritingModePlaceholder')} />\n        </SelectTrigger>\n        <SelectContent>\n          {writingModes.map((mode) => (\n            <SelectItem key={mode.value} value={mode.value}>\n              {t(mode.labelKey)}\n            </SelectItem>\n          ))}\n        </SelectContent>\n      </Select>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AACA;AALA;;;;;;AAYA,MAAM,eAAe;IACnB;QAAE,OAAO;QAAU,UAAU;IAAoB;IACjD;QAAE,OAAO;QAAU,UAAU;IAAoB;IACjD;QAAE,OAAO;QAAgB,UAAU;IAA0B;IAC7D;QAAE,OAAO;QAAY,UAAU;IAAsB;IACrD;QAAE,OAAO;QAAa,UAAU;IAAuB;IACvD;QAAE,OAAO;QAAY,UAAU;IAAsB;IACrD;QAAE,OAAO;QAAY,UAAU;IAAsB;CACtD;AAEM,SAAS,oBAAoB,EAAE,KAAK,EAAE,QAAQ,EAA4B;IAC/E,MAAM,EAAE,CAAC,EAAE,GAAG,CAAA,GAAA,mIAAA,CAAA,UAAO,AAAD;IACpB,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,iIAAA,CAAA,QAAK;gBAAC,SAAQ;gBAAe,WAAU;;kCACtC,8OAAC,wMAAA,CAAA,UAAO;wBAAC,WAAU;;;;;;oBAClB,EAAE;oBAAoB;;;;;;;0BAEzB,8OAAC,kIAAA,CAAA,SAAM;gBAAC,OAAO;gBAAO,eAAe;;kCACnC,8OAAC,kIAAA,CAAA,gBAAa;wBAAC,IAAG;wBAAe,WAAU;kCACzC,cAAA,8OAAC,kIAAA,CAAA,cAAW;4BAAC,aAAa,EAAE;;;;;;;;;;;kCAE9B,8OAAC,kIAAA,CAAA,gBAAa;kCACX,aAAa,GAAG,CAAC,CAAC,qBACjB,8OAAC,kIAAA,CAAA,aAAU;gCAAkB,OAAO,KAAK,KAAK;0CAC3C,EAAE,KAAK,QAAQ;+BADD,KAAK,KAAK;;;;;;;;;;;;;;;;;;;;;;AAQvC", "debugId": null}}, {"offset": {"line": 4236, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/src/components/layout/app-shell.tsx"], "sourcesContent": ["\n\"use client\";\n\nimport React, { type ReactNode } from 'react';\nimport { Sidebar<PERSON>rovider, Sidebar, SidebarHeader, SidebarContent, SidebarFooter, SidebarTrigger, SidebarInset, SidebarMenu, SidebarMenuItem, SidebarMenuButton, SidebarCollapse } from '@/components/ui/sidebar';\nimport { CustomLogo } from '@/components/icons/custom-logo';\nimport { Button } from '@/components/ui/button';\nimport { useI18n } from '@/contexts/i18n-context';\nimport { useAppearance } from '@/contexts/theme-context';\nimport { Moon, Sun, Home, Settings, HelpCircle, User, Globe } from 'lucide-react';\nimport { useState } from 'react';\nimport { SettingsPanel } from '@/components/settings/settings-panel';\nimport { HelpPanel } from '@/components/help/help-panel';\nimport { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';\nimport { APP_SUPPORTED_UI_LANGUAGES } from '@/config/languages';\nimport { Label } from '@/components/ui/label';\nimport { Separator } from '../ui/separator';\nimport { WritingModeSelector } from '../ai/writing-mode-selector';\n\ninterface AppShellProps {\n  children: (props: { writingMode: string }) => ReactNode;\n}\n\nexport function AppShell({ children }: AppShellProps) {\n  const { t, uiLanguage, setUiLanguage } = useI18n();\n  const { theme, setTheme, effectiveTheme } = useAppearance();\n  const [activeView, setActiveView] = useState('editor');\n  const [writingMode, setWritingMode] = useState(\"Formal\");\n\n  const mainContent = () => {\n    switch (activeView) {\n      case 'settings':\n        return <SettingsPanel />;\n      case 'help':\n        return <HelpPanel />;\n      default:\n        return children({ writingMode });\n    }\n  };\n  \n  const sidebarDirection = APP_SUPPORTED_UI_LANGUAGES.find(l => l.value === uiLanguage)?.dir === 'rtl' ? 'right' : 'left';\n\n  return (\n    <SidebarProvider>\n      <Sidebar side={sidebarDirection}>\n        <SidebarHeader>\n          <div className=\"flex items-center gap-2\">\n            <CustomLogo className=\"h-10 w-10\" />\n            <div className=\"flex flex-col group-data-[collapsible=icon]:hidden\">\n                <h2 className=\"text-lg font-semibold text-sidebar-primary\">{t('appName')}</h2>\n                <p className=\"text-xs text-muted-foreground\">{t('appDescription')}</p>\n            </div>\n          </div>\n           <SidebarCollapse className=\"group-data-[collapsible=icon]:hidden\"/>\n        </SidebarHeader>\n        <SidebarContent>\n            <SidebarMenu>\n                <SidebarMenuItem>\n                    <SidebarMenuButton onClick={(e) => { e.preventDefault(); setActiveView('editor'); }} isActive={activeView === 'editor'} tooltip={{ children: t('editorTitle') }}>\n                        <Home/>\n                        <span>{t('editorTitle')}</span>\n                    </SidebarMenuButton>\n                </SidebarMenuItem>\n                 <SidebarMenuItem>\n                    <SidebarMenuButton onClick={(e) => { e.preventDefault(); setActiveView('settings'); }} isActive={activeView === 'settings'} tooltip={{ children: t('settingsTitle') }}>\n                        <Settings />\n                        <span>{t('settingsTitle')}</span>\n                    </SidebarMenuButton>\n                </SidebarMenuItem>\n                 <SidebarMenuItem>\n                    <SidebarMenuButton onClick={(e) => { e.preventDefault(); setActiveView('help'); }} isActive={activeView === 'help'} tooltip={{ children: t('helpTitle') }}>\n                        <HelpCircle />\n                        <span>{t('helpTitle')}</span>\n                    </SidebarMenuButton>\n                </SidebarMenuItem>\n            </SidebarMenu>\n        </SidebarContent>\n        <SidebarFooter className=\"border-t border-sidebar-border\">\n          <Separator className=\"my-1 bg-sidebar-border group-data-[collapsible=icon]:hidden\" />\n          <div className=\"flex items-center justify-between gap-2 group-data-[collapsible=icon]:justify-center p-2\">\n            <Button \n                variant=\"ghost\" \n                size=\"icon\" \n                className=\"text-sidebar-foreground hover:bg-sidebar-accent\"\n                onClick={() => setTheme(effectiveTheme === 'light' ? 'dark' : 'light')} \n                aria-label={effectiveTheme === 'light' ? t('switchToDarkMode') : t('switchToLightMode')}\n            >\n              {effectiveTheme === 'dark' ? <Sun/> : <Moon />}\n            </Button>\n            <Select value={uiLanguage} onValueChange={setUiLanguage}>\n              <SelectTrigger \n                className=\"h-9 text-xs bg-sidebar-background border-sidebar-border text-sidebar-foreground focus:ring-sidebar-ring group-data-[collapsible=icon]:w-9 group-data-[collapsible=icon]:px-2 group-data-[collapsible=icon]:justify-center\"\n                aria-label={t('selectLanguagePlaceholder')}\n              >\n                  <Globe className=\"h-4 w-4\" />\n                  <span className=\"group-data-[collapsible=icon]:hidden\">\n                    <SelectValue placeholder={t('selectLanguagePlaceholder')} />\n                  </span>\n              </SelectTrigger>\n              <SelectContent>\n                {APP_SUPPORTED_UI_LANGUAGES.map((lang) => (\n                  <SelectItem key={lang.value} value={lang.value} className=\"text-xs\">\n                    {t(lang.labelKey)}\n                  </SelectItem>\n                ))}\n              </SelectContent>\n            </Select>\n          </div>\n          <Separator className=\"my-1 bg-sidebar-border group-data-[collapsible=icon]:hidden\" />\n          <div className=\"hidden items-center justify-center group-data-[collapsible=icon]:flex\">\n             <SidebarCollapse />\n          </div>\n        </SidebarFooter>\n      </Sidebar>\n      \n      <SidebarInset className=\"flex flex-col\">\n        <header className=\"sticky top-0 z-10 flex h-14 items-center gap-4 border-b bg-background px-4 sm:px-6\">\n            <SidebarTrigger className=\"md:hidden\"/>\n            <div className=\"flex-1\">\n                <WritingModeSelector value={writingMode} onChange={setWritingMode} />\n            </div>\n        </header>\n        <main className=\"flex-1 overflow-auto\">\n            {mainContent()}\n        </main>\n      </SidebarInset>\n    </SidebarProvider>\n  );\n}\n"], "names": [], "mappings": ";;;;AAIA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AAEA;AACA;AAEA;AACA;AAjBA;;;;;;;;;;;;;;;AAuBO,SAAS,SAAS,EAAE,QAAQ,EAAiB;IAClD,MAAM,EAAE,CAAC,EAAE,UAAU,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,mIAAA,CAAA,UAAO,AAAD;IAC/C,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,cAAc,EAAE,GAAG,CAAA,GAAA,oIAAA,CAAA,gBAAa,AAAD;IACxD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,MAAM,cAAc;QAClB,OAAQ;YACN,KAAK;gBACH,qBAAO,8OAAC,mJAAA,CAAA,gBAAa;;;;;YACvB,KAAK;gBACH,qBAAO,8OAAC,2IAAA,CAAA,YAAS;;;;;YACnB;gBACE,OAAO,SAAS;oBAAE;gBAAY;QAClC;IACF;IAEA,MAAM,mBAAmB,0HAAA,CAAA,6BAA0B,CAAC,IAAI,CAAC,CAAA,IAAK,EAAE,KAAK,KAAK,aAAa,QAAQ,QAAQ,UAAU;IAEjH,qBACE,8OAAC,mIAAA,CAAA,kBAAe;;0BACd,8OAAC,mIAAA,CAAA,UAAO;gBAAC,MAAM;;kCACb,8OAAC,mIAAA,CAAA,gBAAa;;0CACZ,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,6IAAA,CAAA,aAAU;wCAAC,WAAU;;;;;;kDACtB,8OAAC;wCAAI,WAAU;;0DACX,8OAAC;gDAAG,WAAU;0DAA8C,EAAE;;;;;;0DAC9D,8OAAC;gDAAE,WAAU;0DAAiC,EAAE;;;;;;;;;;;;;;;;;;0CAGrD,8OAAC,mIAAA,CAAA,kBAAe;gCAAC,WAAU;;;;;;;;;;;;kCAE9B,8OAAC,mIAAA,CAAA,iBAAc;kCACX,cAAA,8OAAC,mIAAA,CAAA,cAAW;;8CACR,8OAAC,mIAAA,CAAA,kBAAe;8CACZ,cAAA,8OAAC,mIAAA,CAAA,oBAAiB;wCAAC,SAAS,CAAC;4CAAQ,EAAE,cAAc;4CAAI,cAAc;wCAAW;wCAAG,UAAU,eAAe;wCAAU,SAAS;4CAAE,UAAU,EAAE;wCAAe;;0DAC1J,8OAAC,mMAAA,CAAA,OAAI;;;;;0DACL,8OAAC;0DAAM,EAAE;;;;;;;;;;;;;;;;;8CAGhB,8OAAC,mIAAA,CAAA,kBAAe;8CACb,cAAA,8OAAC,mIAAA,CAAA,oBAAiB;wCAAC,SAAS,CAAC;4CAAQ,EAAE,cAAc;4CAAI,cAAc;wCAAa;wCAAG,UAAU,eAAe;wCAAY,SAAS;4CAAE,UAAU,EAAE;wCAAiB;;0DAChK,8OAAC,0MAAA,CAAA,WAAQ;;;;;0DACT,8OAAC;0DAAM,EAAE;;;;;;;;;;;;;;;;;8CAGhB,8OAAC,mIAAA,CAAA,kBAAe;8CACb,cAAA,8OAAC,mIAAA,CAAA,oBAAiB;wCAAC,SAAS,CAAC;4CAAQ,EAAE,cAAc;4CAAI,cAAc;wCAAS;wCAAG,UAAU,eAAe;wCAAQ,SAAS;4CAAE,UAAU,EAAE;wCAAa;;0DACpJ,8OAAC,kNAAA,CAAA,aAAU;;;;;0DACX,8OAAC;0DAAM,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAKzB,8OAAC,mIAAA,CAAA,gBAAa;wBAAC,WAAU;;0CACvB,8OAAC,qIAAA,CAAA,YAAS;gCAAC,WAAU;;;;;;0CACrB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,kIAAA,CAAA,SAAM;wCACH,SAAQ;wCACR,MAAK;wCACL,WAAU;wCACV,SAAS,IAAM,SAAS,mBAAmB,UAAU,SAAS;wCAC9D,cAAY,mBAAmB,UAAU,EAAE,sBAAsB,EAAE;kDAEpE,mBAAmB,uBAAS,8OAAC,gMAAA,CAAA,MAAG;;;;iEAAK,8OAAC,kMAAA,CAAA,OAAI;;;;;;;;;;kDAE7C,8OAAC,kIAAA,CAAA,SAAM;wCAAC,OAAO;wCAAY,eAAe;;0DACxC,8OAAC,kIAAA,CAAA,gBAAa;gDACZ,WAAU;gDACV,cAAY,EAAE;;kEAEZ,8OAAC,oMAAA,CAAA,QAAK;wDAAC,WAAU;;;;;;kEACjB,8OAAC;wDAAK,WAAU;kEACd,cAAA,8OAAC,kIAAA,CAAA,cAAW;4DAAC,aAAa,EAAE;;;;;;;;;;;;;;;;;0DAGlC,8OAAC,kIAAA,CAAA,gBAAa;0DACX,0HAAA,CAAA,6BAA0B,CAAC,GAAG,CAAC,CAAC,qBAC/B,8OAAC,kIAAA,CAAA,aAAU;wDAAkB,OAAO,KAAK,KAAK;wDAAE,WAAU;kEACvD,EAAE,KAAK,QAAQ;uDADD,KAAK,KAAK;;;;;;;;;;;;;;;;;;;;;;0CAOnC,8OAAC,qIAAA,CAAA,YAAS;gCAAC,WAAU;;;;;;0CACrB,8OAAC;gCAAI,WAAU;0CACZ,cAAA,8OAAC,mIAAA,CAAA,kBAAe;;;;;;;;;;;;;;;;;;;;;;0BAKvB,8OAAC,mIAAA,CAAA,eAAY;gBAAC,WAAU;;kCACtB,8OAAC;wBAAO,WAAU;;0CACd,8OAAC,mIAAA,CAAA,iBAAc;gCAAC,WAAU;;;;;;0CAC1B,8OAAC;gCAAI,WAAU;0CACX,cAAA,8OAAC,uJAAA,CAAA,sBAAmB;oCAAC,OAAO;oCAAa,UAAU;;;;;;;;;;;;;;;;;kCAG3D,8OAAC;wBAAK,WAAU;kCACX;;;;;;;;;;;;;;;;;;AAKb", "debugId": null}}, {"offset": {"line": 4663, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/src/components/editor/document-toolbar.tsx"], "sourcesContent": ["\"use client\";\n\nimport React from 'react';\nimport { Button } from '@/components/ui/button';\nimport { Separator } from '@/components/ui/separator';\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';\nimport { useI18n } from '@/contexts/i18n-context';\nimport { \n  Undo2, \n  Redo2, \n  Save, \n  FileText, \n  Type, \n  Palette,\n  ZoomIn,\n  ZoomOut,\n  RotateCcw\n} from 'lucide-react';\n\ninterface DocumentToolbarProps {\n  onUndo: () => void;\n  onRedo: () => void;\n  canUndo: boolean;\n  canRedo: boolean;\n  onSave?: () => void;\n  onExport?: () => void;\n  fontSize: number;\n  onFontSizeChange: (size: number) => void;\n  fontFamily: string;\n  onFontFamilyChange: (family: string) => void;\n  writingMode: string;\n  onWritingModeChange: (mode: string) => void;\n}\n\nconst fontOptions = [\n  { value: \"'Inter', sans-serif\", label: 'Inter' },\n  { value: \"'Source Code Pro', monospace\", label: 'Source Code Pro' },\n  { value: \"'Georgia', serif\", label: 'Georgia' },\n  { value: \"'Times New Roman', Times, serif\", label: 'Times New Roman' },\n  { value: \"'Arial', sans-serif\", label: 'Arial' },\n  { value: \"'Helvetica', sans-serif\", label: 'Helvetica' },\n];\n\nconst writingModes = [\n  { value: 'formal', label: 'Formal' },\n  { value: 'casual', label: 'Casual' },\n  { value: 'academic', label: 'Academic' },\n  { value: 'creative', label: 'Creative' },\n  { value: 'business', label: 'Business' },\n  { value: 'technical', label: 'Technical' },\n];\n\nexport function DocumentToolbar({\n  onUndo,\n  onRedo,\n  canUndo,\n  canRedo,\n  onSave,\n  onExport,\n  fontSize,\n  onFontSizeChange,\n  fontFamily,\n  onFontFamilyChange,\n  writingMode,\n  onWritingModeChange,\n}: DocumentToolbarProps) {\n  const { t } = useI18n();\n\n  const handleFontSizeIncrease = () => {\n    if (fontSize < 24) {\n      onFontSizeChange(fontSize + 1);\n    }\n  };\n\n  const handleFontSizeDecrease = () => {\n    if (fontSize > 10) {\n      onFontSizeChange(fontSize - 1);\n    }\n  };\n\n  const handleFontSizeReset = () => {\n    onFontSizeChange(16);\n  };\n\n  return (\n    <div className=\"flex items-center gap-2 p-3 border-b bg-card\">\n      {/* File Operations */}\n      <div className=\"flex items-center gap-1\">\n        {onSave && (\n          <Button variant=\"ghost\" size=\"sm\" onClick={onSave}>\n            <Save className=\"h-4 w-4 mr-1\" />\n            {t('saveButton')}\n          </Button>\n        )}\n        {onExport && (\n          <Button variant=\"ghost\" size=\"sm\" onClick={onExport}>\n            <FileText className=\"h-4 w-4 mr-1\" />\n            {t('exportButton')}\n          </Button>\n        )}\n      </div>\n\n      <Separator orientation=\"vertical\" className=\"h-6\" />\n\n      {/* Undo/Redo */}\n      <div className=\"flex items-center gap-1\">\n        <Button \n          variant=\"ghost\" \n          size=\"sm\" \n          onClick={onUndo} \n          disabled={!canUndo}\n          title={t('undoButton')}\n        >\n          <Undo2 className=\"h-4 w-4\" />\n        </Button>\n        <Button \n          variant=\"ghost\" \n          size=\"sm\" \n          onClick={onRedo} \n          disabled={!canRedo}\n          title={t('redoButton')}\n        >\n          <Redo2 className=\"h-4 w-4\" />\n        </Button>\n      </div>\n\n      <Separator orientation=\"vertical\" className=\"h-6\" />\n\n      {/* Font Controls */}\n      <div className=\"flex items-center gap-2\">\n        <Type className=\"h-4 w-4 text-muted-foreground\" />\n        <Select value={fontFamily} onValueChange={onFontFamilyChange}>\n          <SelectTrigger className=\"w-40\">\n            <SelectValue />\n          </SelectTrigger>\n          <SelectContent>\n            {fontOptions.map((font) => (\n              <SelectItem key={font.value} value={font.value}>\n                <span style={{ fontFamily: font.value }}>{font.label}</span>\n              </SelectItem>\n            ))}\n          </SelectContent>\n        </Select>\n\n        <div className=\"flex items-center gap-1 border rounded-md\">\n          <Button \n            variant=\"ghost\" \n            size=\"sm\" \n            onClick={handleFontSizeDecrease}\n            disabled={fontSize <= 10}\n            className=\"h-8 w-8 p-0\"\n          >\n            <ZoomOut className=\"h-3 w-3\" />\n          </Button>\n          <span className=\"px-2 text-sm font-medium min-w-[2rem] text-center\">\n            {fontSize}\n          </span>\n          <Button \n            variant=\"ghost\" \n            size=\"sm\" \n            onClick={handleFontSizeIncrease}\n            disabled={fontSize >= 24}\n            className=\"h-8 w-8 p-0\"\n          >\n            <ZoomIn className=\"h-3 w-3\" />\n          </Button>\n          <Button \n            variant=\"ghost\" \n            size=\"sm\" \n            onClick={handleFontSizeReset}\n            className=\"h-8 w-8 p-0\"\n            title=\"Reset font size\"\n          >\n            <RotateCcw className=\"h-3 w-3\" />\n          </Button>\n        </div>\n      </div>\n\n      <Separator orientation=\"vertical\" className=\"h-6\" />\n\n      {/* Writing Mode */}\n      <div className=\"flex items-center gap-2\">\n        <Palette className=\"h-4 w-4 text-muted-foreground\" />\n        <Select value={writingMode} onValueChange={onWritingModeChange}>\n          <SelectTrigger className=\"w-32\">\n            <SelectValue />\n          </SelectTrigger>\n          <SelectContent>\n            {writingModes.map((mode) => (\n              <SelectItem key={mode.value} value={mode.value}>\n                {mode.label}\n              </SelectItem>\n            ))}\n          </SelectContent>\n        </Select>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAPA;;;;;;;AAkCA,MAAM,cAAc;IAClB;QAAE,OAAO;QAAuB,OAAO;IAAQ;IAC/C;QAAE,OAAO;QAAgC,OAAO;IAAkB;IAClE;QAAE,OAAO;QAAoB,OAAO;IAAU;IAC9C;QAAE,OAAO;QAAmC,OAAO;IAAkB;IACrE;QAAE,OAAO;QAAuB,OAAO;IAAQ;IAC/C;QAAE,OAAO;QAA2B,OAAO;IAAY;CACxD;AAED,MAAM,eAAe;IACnB;QAAE,OAAO;QAAU,OAAO;IAAS;IACnC;QAAE,OAAO;QAAU,OAAO;IAAS;IACnC;QAAE,OAAO;QAAY,OAAO;IAAW;IACvC;QAAE,OAAO;QAAY,OAAO;IAAW;IACvC;QAAE,OAAO;QAAY,OAAO;IAAW;IACvC;QAAE,OAAO;QAAa,OAAO;IAAY;CAC1C;AAEM,SAAS,gBAAgB,EAC9B,MAAM,EACN,MAAM,EACN,OAAO,EACP,OAAO,EACP,MAAM,EACN,QAAQ,EACR,QAAQ,EACR,gBAAgB,EAChB,UAAU,EACV,kBAAkB,EAClB,WAAW,EACX,mBAAmB,EACE;IACrB,MAAM,EAAE,CAAC,EAAE,GAAG,CAAA,GAAA,mIAAA,CAAA,UAAO,AAAD;IAEpB,MAAM,yBAAyB;QAC7B,IAAI,WAAW,IAAI;YACjB,iBAAiB,WAAW;QAC9B;IACF;IAEA,MAAM,yBAAyB;QAC7B,IAAI,WAAW,IAAI;YACjB,iBAAiB,WAAW;QAC9B;IACF;IAEA,MAAM,sBAAsB;QAC1B,iBAAiB;IACnB;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;oBACZ,wBACC,8OAAC,kIAAA,CAAA,SAAM;wBAAC,SAAQ;wBAAQ,MAAK;wBAAK,SAAS;;0CACzC,8OAAC,kMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;4BACf,EAAE;;;;;;;oBAGN,0BACC,8OAAC,kIAAA,CAAA,SAAM;wBAAC,SAAQ;wBAAQ,MAAK;wBAAK,SAAS;;0CACzC,8OAAC,8MAAA,CAAA,WAAQ;gCAAC,WAAU;;;;;;4BACnB,EAAE;;;;;;;;;;;;;0BAKT,8OAAC,qIAAA,CAAA,YAAS;gBAAC,aAAY;gBAAW,WAAU;;;;;;0BAG5C,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,kIAAA,CAAA,SAAM;wBACL,SAAQ;wBACR,MAAK;wBACL,SAAS;wBACT,UAAU,CAAC;wBACX,OAAO,EAAE;kCAET,cAAA,8OAAC,wMAAA,CAAA,QAAK;4BAAC,WAAU;;;;;;;;;;;kCAEnB,8OAAC,kIAAA,CAAA,SAAM;wBACL,SAAQ;wBACR,MAAK;wBACL,SAAS;wBACT,UAAU,CAAC;wBACX,OAAO,EAAE;kCAET,cAAA,8OAAC,wMAAA,CAAA,QAAK;4BAAC,WAAU;;;;;;;;;;;;;;;;;0BAIrB,8OAAC,qIAAA,CAAA,YAAS;gBAAC,aAAY;gBAAW,WAAU;;;;;;0BAG5C,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,kMAAA,CAAA,OAAI;wBAAC,WAAU;;;;;;kCAChB,8OAAC,kIAAA,CAAA,SAAM;wBAAC,OAAO;wBAAY,eAAe;;0CACxC,8OAAC,kIAAA,CAAA,gBAAa;gCAAC,WAAU;0CACvB,cAAA,8OAAC,kIAAA,CAAA,cAAW;;;;;;;;;;0CAEd,8OAAC,kIAAA,CAAA,gBAAa;0CACX,YAAY,GAAG,CAAC,CAAC,qBAChB,8OAAC,kIAAA,CAAA,aAAU;wCAAkB,OAAO,KAAK,KAAK;kDAC5C,cAAA,8OAAC;4CAAK,OAAO;gDAAE,YAAY,KAAK,KAAK;4CAAC;sDAAI,KAAK,KAAK;;;;;;uCADrC,KAAK,KAAK;;;;;;;;;;;;;;;;kCAOjC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,kIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,SAAS;gCACT,UAAU,YAAY;gCACtB,WAAU;0CAEV,cAAA,8OAAC,4MAAA,CAAA,UAAO;oCAAC,WAAU;;;;;;;;;;;0CAErB,8OAAC;gCAAK,WAAU;0CACb;;;;;;0CAEH,8OAAC,kIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,SAAS;gCACT,UAAU,YAAY;gCACtB,WAAU;0CAEV,cAAA,8OAAC,0MAAA,CAAA,SAAM;oCAAC,WAAU;;;;;;;;;;;0CAEpB,8OAAC,kIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,SAAS;gCACT,WAAU;gCACV,OAAM;0CAEN,cAAA,8OAAC,gNAAA,CAAA,YAAS;oCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;0BAK3B,8OAAC,qIAAA,CAAA,YAAS;gBAAC,aAAY;gBAAW,WAAU;;;;;;0BAG5C,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,wMAAA,CAAA,UAAO;wBAAC,WAAU;;;;;;kCACnB,8OAAC,kIAAA,CAAA,SAAM;wBAAC,OAAO;wBAAa,eAAe;;0CACzC,8OAAC,kIAAA,CAAA,gBAAa;gCAAC,WAAU;0CACvB,cAAA,8OAAC,kIAAA,CAAA,cAAW;;;;;;;;;;0CAEd,8OAAC,kIAAA,CAAA,gBAAa;0CACX,aAAa,GAAG,CAAC,CAAC,qBACjB,8OAAC,kIAAA,CAAA,aAAU;wCAAkB,OAAO,KAAK,KAAK;kDAC3C,KAAK,KAAK;uCADI,KAAK,KAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASzC", "debugId": null}}, {"offset": {"line": 5070, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground hover:bg-primary/80\",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n        destructive:\n          \"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80\",\n        outline: \"text-foreground\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nexport interface BadgeProps\n  extends React.HTMLAttributes<HTMLDivElement>,\n    VariantProps<typeof badgeVariants> {}\n\nfunction Badge({ className, variant, ...props }: BadgeProps) {\n  return (\n    <div className={cn(badgeVariants({ variant }), className)} {...props} />\n  )\n}\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;AACA;AAEA;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACtB,0KACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SAAS;QACX;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAOF,SAAS,MAAM,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG,OAAmB;IACzD,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QAAa,GAAG,KAAK;;;;;;AAExE", "debugId": null}}, {"offset": {"line": 5112, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/src/components/editor/document-status-bar.tsx"], "sourcesContent": ["\"use client\";\n\nimport React from 'react';\nimport { Badge } from '@/components/ui/badge';\nimport { Separator } from '@/components/ui/separator';\nimport { useI18n } from '@/contexts/i18n-context';\nimport { \n  FileText, \n  Clock, \n  Target, \n  AlertTriangle, \n  CheckCircle,\n  ShieldCheck,\n  Sparkles,\n  Edit3\n} from 'lucide-react';\n\ninterface DocumentStatusBarProps {\n  wordCount: number;\n  charCount: number;\n  readingTime: number;\n  writingScore: number;\n  spellingErrors: number;\n  grammarErrors: number;\n  styleIssues: number;\n  vocabularyEnhancements: number;\n  plagiarismIssues: number;\n  similarityIssues: number;\n  language: string;\n  isAnalyzing: boolean;\n}\n\nexport function DocumentStatusBar({\n  wordCount,\n  charCount,\n  readingTime,\n  writingScore,\n  spellingErrors,\n  grammarErrors,\n  styleIssues,\n  vocabularyEnhancements,\n  plagiarismIssues,\n  similarityIssues,\n  language,\n  isAnalyzing,\n}: DocumentStatusBarProps) {\n  const { t } = useI18n();\n\n  const totalIssues = spellingErrors + grammarErrors + styleIssues + plagiarismIssues + similarityIssues;\n  const totalEnhancements = vocabularyEnhancements;\n\n  const getScoreColor = (score: number) => {\n    if (score >= 80) return 'text-green-600';\n    if (score >= 60) return 'text-yellow-600';\n    return 'text-red-600';\n  };\n\n  const getScoreBadgeVariant = (score: number): \"default\" | \"secondary\" | \"destructive\" | \"outline\" => {\n    if (score >= 80) return 'default';\n    if (score >= 60) return 'secondary';\n    return 'destructive';\n  };\n\n  return (\n    <div className=\"flex items-center justify-between gap-4 p-3 border-t bg-muted/30 text-sm\">\n      {/* Left side - Document stats */}\n      <div className=\"flex items-center gap-4\">\n        <div className=\"flex items-center gap-2\">\n          <FileText className=\"h-4 w-4 text-muted-foreground\" />\n          <span className=\"font-medium\">{wordCount}</span>\n          <span className=\"text-muted-foreground\">{t('wordsLabel')}</span>\n        </div>\n\n        <Separator orientation=\"vertical\" className=\"h-4\" />\n\n        <div className=\"flex items-center gap-2\">\n          <span className=\"font-medium\">{charCount}</span>\n          <span className=\"text-muted-foreground\">{t('charactersLabel')}</span>\n        </div>\n\n        <Separator orientation=\"vertical\" className=\"h-4\" />\n\n        <div className=\"flex items-center gap-2\">\n          <Clock className=\"h-4 w-4 text-muted-foreground\" />\n          <span className=\"font-medium\">{readingTime}</span>\n          <span className=\"text-muted-foreground\">{t('minReadLabel')}</span>\n        </div>\n\n        <Separator orientation=\"vertical\" className=\"h-4\" />\n\n        <div className=\"flex items-center gap-2\">\n          <span className=\"text-muted-foreground\">{t('languageLabel')}:</span>\n          <Badge variant=\"outline\" className=\"text-xs\">\n            {language.toUpperCase()}\n          </Badge>\n        </div>\n      </div>\n\n      {/* Center - Analysis status */}\n      <div className=\"flex items-center gap-3\">\n        {isAnalyzing && (\n          <div className=\"flex items-center gap-2 text-blue-600\">\n            <div className=\"animate-spin rounded-full h-3 w-3 border-b-2 border-blue-600\"></div>\n            <span className=\"text-xs\">{t('analyzingLabel')}</span>\n          </div>\n        )}\n\n        {/* Issue indicators */}\n        {!isAnalyzing && (\n          <div className=\"flex items-center gap-3\">\n            {spellingErrors > 0 && (\n              <div className=\"flex items-center gap-1 text-red-600\">\n                <AlertTriangle className=\"h-3 w-3\" />\n                <span className=\"text-xs font-medium\">{spellingErrors}</span>\n                <span className=\"text-xs\">{t('spellingLabel')}</span>\n              </div>\n            )}\n\n            {grammarErrors > 0 && (\n              <div className=\"flex items-center gap-1 text-red-600\">\n                <AlertTriangle className=\"h-3 w-3\" />\n                <span className=\"text-xs font-medium\">{grammarErrors}</span>\n                <span className=\"text-xs\">{t('grammarLabel')}</span>\n              </div>\n            )}\n\n            {styleIssues > 0 && (\n              <div className=\"flex items-center gap-1 text-blue-600\">\n                <Edit3 className=\"h-3 w-3\" />\n                <span className=\"text-xs font-medium\">{styleIssues}</span>\n                <span className=\"text-xs\">{t('styleLabel')}</span>\n              </div>\n            )}\n\n            {vocabularyEnhancements > 0 && (\n              <div className=\"flex items-center gap-1 text-green-600\">\n                <Sparkles className=\"h-3 w-3\" />\n                <span className=\"text-xs font-medium\">{vocabularyEnhancements}</span>\n                <span className=\"text-xs\">{t('vocabularyLabel')}</span>\n              </div>\n            )}\n\n            {plagiarismIssues > 0 && (\n              <div className=\"flex items-center gap-1 text-red-700\">\n                <ShieldCheck className=\"h-3 w-3\" />\n                <span className=\"text-xs font-medium\">{plagiarismIssues}</span>\n                <span className=\"text-xs\">{t('plagiarismLabel')}</span>\n              </div>\n            )}\n\n            {similarityIssues > 0 && (\n              <div className=\"flex items-center gap-1 text-yellow-600\">\n                <AlertTriangle className=\"h-3 w-3\" />\n                <span className=\"text-xs font-medium\">{similarityIssues}</span>\n                <span className=\"text-xs\">{t('similarityLabel')}</span>\n              </div>\n            )}\n\n            {totalIssues === 0 && totalEnhancements === 0 && !isAnalyzing && (\n              <div className=\"flex items-center gap-1 text-green-600\">\n                <CheckCircle className=\"h-3 w-3\" />\n                <span className=\"text-xs\">{t('noIssuesLabel')}</span>\n              </div>\n            )}\n          </div>\n        )}\n      </div>\n\n      {/* Right side - Writing score */}\n      <div className=\"flex items-center gap-2\">\n        <Target className=\"h-4 w-4 text-muted-foreground\" />\n        <span className=\"text-muted-foreground\">{t('scoreLabel')}:</span>\n        <Badge variant={getScoreBadgeVariant(writingScore)} className=\"font-medium\">\n          {writingScore}/100\n        </Badge>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AANA;;;;;;AAgCO,SAAS,kBAAkB,EAChC,SAAS,EACT,SAAS,EACT,WAAW,EACX,YAAY,EACZ,cAAc,EACd,aAAa,EACb,WAAW,EACX,sBAAsB,EACtB,gBAAgB,EAChB,gBAAgB,EAChB,QAAQ,EACR,WAAW,EACY;IACvB,MAAM,EAAE,CAAC,EAAE,GAAG,CAAA,GAAA,mIAAA,CAAA,UAAO,AAAD;IAEpB,MAAM,cAAc,iBAAiB,gBAAgB,cAAc,mBAAmB;IACtF,MAAM,oBAAoB;IAE1B,MAAM,gBAAgB,CAAC;QACrB,IAAI,SAAS,IAAI,OAAO;QACxB,IAAI,SAAS,IAAI,OAAO;QACxB,OAAO;IACT;IAEA,MAAM,uBAAuB,CAAC;QAC5B,IAAI,SAAS,IAAI,OAAO;QACxB,IAAI,SAAS,IAAI,OAAO;QACxB,OAAO;IACT;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,8MAAA,CAAA,WAAQ;gCAAC,WAAU;;;;;;0CACpB,8OAAC;gCAAK,WAAU;0CAAe;;;;;;0CAC/B,8OAAC;gCAAK,WAAU;0CAAyB,EAAE;;;;;;;;;;;;kCAG7C,8OAAC,qIAAA,CAAA,YAAS;wBAAC,aAAY;wBAAW,WAAU;;;;;;kCAE5C,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAK,WAAU;0CAAe;;;;;;0CAC/B,8OAAC;gCAAK,WAAU;0CAAyB,EAAE;;;;;;;;;;;;kCAG7C,8OAAC,qIAAA,CAAA,YAAS;wBAAC,aAAY;wBAAW,WAAU;;;;;;kCAE5C,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,oMAAA,CAAA,QAAK;gCAAC,WAAU;;;;;;0CACjB,8OAAC;gCAAK,WAAU;0CAAe;;;;;;0CAC/B,8OAAC;gCAAK,WAAU;0CAAyB,EAAE;;;;;;;;;;;;kCAG7C,8OAAC,qIAAA,CAAA,YAAS;wBAAC,aAAY;wBAAW,WAAU;;;;;;kCAE5C,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAK,WAAU;;oCAAyB,EAAE;oCAAiB;;;;;;;0CAC5D,8OAAC,iIAAA,CAAA,QAAK;gCAAC,SAAQ;gCAAU,WAAU;0CAChC,SAAS,WAAW;;;;;;;;;;;;;;;;;;0BAM3B,8OAAC;gBAAI,WAAU;;oBACZ,6BACC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;;;;;0CACf,8OAAC;gCAAK,WAAU;0CAAW,EAAE;;;;;;;;;;;;oBAKhC,CAAC,6BACA,8OAAC;wBAAI,WAAU;;4BACZ,iBAAiB,mBAChB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,wNAAA,CAAA,gBAAa;wCAAC,WAAU;;;;;;kDACzB,8OAAC;wCAAK,WAAU;kDAAuB;;;;;;kDACvC,8OAAC;wCAAK,WAAU;kDAAW,EAAE;;;;;;;;;;;;4BAIhC,gBAAgB,mBACf,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,wNAAA,CAAA,gBAAa;wCAAC,WAAU;;;;;;kDACzB,8OAAC;wCAAK,WAAU;kDAAuB;;;;;;kDACvC,8OAAC;wCAAK,WAAU;kDAAW,EAAE;;;;;;;;;;;;4BAIhC,cAAc,mBACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,0MAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;kDACjB,8OAAC;wCAAK,WAAU;kDAAuB;;;;;;kDACvC,8OAAC;wCAAK,WAAU;kDAAW,EAAE;;;;;;;;;;;;4BAIhC,yBAAyB,mBACxB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,0MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;kDACpB,8OAAC;wCAAK,WAAU;kDAAuB;;;;;;kDACvC,8OAAC;wCAAK,WAAU;kDAAW,EAAE;;;;;;;;;;;;4BAIhC,mBAAmB,mBAClB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,oNAAA,CAAA,cAAW;wCAAC,WAAU;;;;;;kDACvB,8OAAC;wCAAK,WAAU;kDAAuB;;;;;;kDACvC,8OAAC;wCAAK,WAAU;kDAAW,EAAE;;;;;;;;;;;;4BAIhC,mBAAmB,mBAClB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,wNAAA,CAAA,gBAAa;wCAAC,WAAU;;;;;;kDACzB,8OAAC;wCAAK,WAAU;kDAAuB;;;;;;kDACvC,8OAAC;wCAAK,WAAU;kDAAW,EAAE;;;;;;;;;;;;4BAIhC,gBAAgB,KAAK,sBAAsB,KAAK,CAAC,6BAChD,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,2NAAA,CAAA,cAAW;wCAAC,WAAU;;;;;;kDACvB,8OAAC;wCAAK,WAAU;kDAAW,EAAE;;;;;;;;;;;;;;;;;;;;;;;;0BAQvC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,sMAAA,CAAA,SAAM;wBAAC,WAAU;;;;;;kCAClB,8OAAC;wBAAK,WAAU;;4BAAyB,EAAE;4BAAc;;;;;;;kCACzD,8OAAC,iIAAA,CAAA,QAAK;wBAAC,SAAS,qBAAqB;wBAAe,WAAU;;4BAC3D;4BAAa;;;;;;;;;;;;;;;;;;;AAKxB", "debugId": null}}, {"offset": {"line": 5611, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/src/components/ui/textarea.tsx"], "sourcesContent": ["import * as React from 'react';\n\nimport {cn} from '@/lib/utils';\n\nconst Textarea = React.forwardRef<HTMLTextAreaElement, React.ComponentProps<'textarea'>>(\n  ({className, ...props}, ref) => {\n    return (\n      <textarea\n        className={cn(\n          'flex min-h-[80px] w-full rounded-md border border-input bg-transparent px-3 py-2 text-base ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm',\n          className\n        )}\n        ref={ref}\n        {...props}\n      />\n    );\n  }\n);\nTextarea.displayName = 'Textarea';\n\nexport {Textarea};\n"], "names": [], "mappings": ";;;;AAAA;AAEA;;;;AAEA,MAAM,yBAAW,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAC9B,CAAC,EAAC,SAAS,EAAE,GAAG,OAAM,EAAE;IACtB,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,sTACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AAEF,SAAS,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 5639, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/src/hooks/use-debounce.ts"], "sourcesContent": ["\n'use client';\n\nimport { useState, useEffect } from 'react';\n\nexport function useDebounce<T>(value: T, delay: number): T {\n  const [debouncedValue, setDebouncedValue] = useState<T>(value);\n\n  useEffect(() => {\n    // Set debouncedValue to value (passed in) after the specified delay\n    const handler = setTimeout(() => {\n      setDebouncedValue(value);\n    }, delay);\n\n    // Return a cleanup function that will be called every time ...\n    // ... useEffect is re-called. useEffect will only be re-called ...\n    // ... if value or delay changes (see the inputs array below). \n    // This is how we prevent debouncedValue from changing if value is ...\n    // ... changing within the delay period. Timeout gets cleared and restarted.\n    return () => {\n      clearTimeout(handler);\n    };\n  }, [value, delay]); // Only re-call effect if value or delay changes\n\n  return debouncedValue;\n}\n"], "names": [], "mappings": ";;;AAGA;AAFA;;AAIO,SAAS,YAAe,KAAQ,EAAE,KAAa;IACpD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAK;IAExD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,oEAAoE;QACpE,MAAM,UAAU,WAAW;YACzB,kBAAkB;QACpB,GAAG;QAEH,+DAA+D;QAC/D,mEAAmE;QACnE,+DAA+D;QAC/D,sEAAsE;QACtE,4EAA4E;QAC5E,OAAO;YACL,aAAa;QACf;IACF,GAAG;QAAC;QAAO;KAAM,GAAG,gDAAgD;IAEpE,OAAO;AACT", "debugId": null}}, {"offset": {"line": 5672, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/src/components/editor/enhanced-document-editor.tsx"], "sourcesContent": ["\"use client\";\n\nimport React, { useState, useEffect, useCallback, useRef, useMemo, forwardRef } from 'react';\nimport { Card, CardContent } from '@/components/ui/card';\nimport { Textarea } from '@/components/ui/textarea';\nimport { Button } from '@/components/ui/button';\nimport { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';\nimport { useToast } from '@/hooks/use-toast';\nimport { useI18n } from '@/contexts/i18n-context';\nimport { useDebounce } from '@/hooks/use-debounce';\nimport { cn } from '@/lib/utils';\nimport { Check, X, AlertTriangle, ShieldCheck, Sparkles, Edit3 } from 'lucide-react';\n\n// Removed type import - now defined locally\ntype AnalysisSuggestion = {\n  id: string;\n  type: 'style' | 'spelling' | 'grammar' | 'rewrite';\n  message: string;\n  suggestion: string;\n  originalSegment: string;\n  severity: 'low' | 'medium' | 'high';\n  startIndex?: number;\n  endIndex?: number;\n  suggestions?: string[];\n};\n\n// Legacy compatibility types\nexport interface PlagiarismSource {\n  id: string;\n  type: 'style' | 'spelling' | 'grammar' | 'rewrite';\n  message: string;\n  suggestion: string;\n  originalSegment: string;\n  severity: 'low' | 'medium' | 'high';\n  startIndex?: number;\n  endIndex?: number;\n  url?: string;\n  title?: string;\n  similarity?: number;\n  matchedText?: string;\n}\n\nexport interface SimilaritySource {\n  url: string;\n  title: string;\n  similarity: number;\n  matchedText: string;\n}\n\n// Enhanced suggestion types with new categories\nexport interface EnhancedSuggestion extends AnalysisSuggestion {\n  category: 'spelling' | 'grammar' | 'style' | 'vocabulary' | 'plagiarism' | 'similarity';\n  confidence: number;\n  replacements?: string[];\n  explanation?: string;\n  source?: string;\n  context?: {\n    before: string;\n    after: string;\n  };\n}\n\n// Enhanced document analysis interface\nexport interface DocumentAnalysis {\n  suggestions: EnhancedSuggestion[];\n  metrics: {\n    readabilityScore: number;\n    wordCount: number;\n    sentenceCount: number;\n    avgWordsPerSentence: number;\n    complexWords: number;\n    passiveVoice: number;\n  };\n  plagiarismCheck?: {\n    similarity: number;\n    sources: Array<{\n      url: string;\n      title: string;\n      similarity: number;\n      matchedText: string;\n    }>;\n  };\n}\n\n// Enhanced props interface\nexport interface EnhancedDocumentEditorProps {\n  value: string;\n  onChange: (value: string) => void;\n  onAnalysisComplete?: (analysis: DocumentAnalysis) => void;\n  placeholder?: string;\n  disabled?: boolean;\n  autoAnalyze?: boolean;\n  analysisDelay?: number;\n  showMetrics?: boolean;\n  enablePlagiarismCheck?: boolean;\n  enableGrammarCheck?: boolean;\n  enableStyleSuggestions?: boolean;\n  enableVocabularyEnhancement?: boolean;\n  maxSuggestions?: number;\n  language?: string;\n  suggestions?: EnhancedSuggestion[];\n  onApplySuggestion?: (suggestion: EnhancedSuggestion) => void;\n  onDismissSuggestion?: (suggestionId: string) => void;\n  isAnalyzing?: boolean;\n  analysisProgress?: number;\n  similaritySources?: Array<{\n    url: string;\n    title: string;\n    similarity: number;\n    matchedText: string;\n  }>;\n  plagiarismSources?: PlagiarismSource[];\n  writingMode?: 'formal' | 'casual' | 'academic' | 'creative';\n  direction?: 'ltr' | 'rtl';\n  onUndo?: () => void;\n  onRedo?: () => void;\n  canUndo?: boolean;\n  canRedo?: boolean;\n  className?: string;\n}\n\n// Enhanced Document Editor Component\nconst EnhancedDocumentEditor = forwardRef<HTMLTextAreaElement, EnhancedDocumentEditorProps>(({\n    value,\n    onChange,\n    onAnalysisComplete,\n    placeholder = \"Start writing your document...\",\n    disabled = false,\n    autoAnalyze = true,\n    analysisDelay = 1000,\n    showMetrics = true,\n    enablePlagiarismCheck = false,\n    enableGrammarCheck = true,\n    enableStyleSuggestions = true,\n    enableVocabularyEnhancement = true,\n    maxSuggestions = 10,\n    language = 'en',\n    suggestions = [],\n    isAnalyzing = false,\n    analysisProgress = 0,\n    similaritySources = [],\n    plagiarismSources = [],\n    onApplySuggestion,\n    onDismissSuggestion,\n    writingMode,\n    direction,\n    onUndo,\n    onRedo,\n    canUndo,\n    canRedo,\n    className,\n  }, ref) => {\n    const { t } = useI18n();\n    const { toast } = useToast();\n    const textareaRef = useRef<HTMLTextAreaElement>(null);\n    const [selectedSuggestion, setSelectedSuggestion] = useState<EnhancedSuggestion | null>(null);\n    const [popoverOpen, setPopoverOpen] = useState(false);\n    const [cursorPosition, setCursorPosition] = useState(0);\n    const [localSuggestions, setLocalSuggestions] = useState<EnhancedSuggestion[]>(suggestions);\n    \n    // Debounced value for analysis\n    const debouncedValue = useDebounce(value, analysisDelay);\n    \n    // Mock analysis function - in real implementation, this would call your AI service\n    const analyzeDocument = useCallback(async (text: string): Promise<DocumentAnalysis> => {\n      // Simulate API delay\n      await new Promise(resolve => setTimeout(resolve, 1000));\n      \n      // Mock analysis results\n      const mockSuggestions: EnhancedSuggestion[] = [\n        {\n          id: '1',\n          type: 'grammar',\n          category: 'grammar',\n          message: 'Consider using active voice',\n          suggestion: 'The team completed the project',\n          originalSegment: 'The project was completed by the team',\n          severity: 'medium',\n          confidence: 0.85,\n          startIndex: 0,\n          endIndex: 35,\n          explanation: 'Active voice makes your writing more direct and engaging.'\n        }\n      ];\n      \n      return {\n        suggestions: mockSuggestions,\n        metrics: {\n          readabilityScore: 75,\n          wordCount: text.split(' ').length,\n          sentenceCount: text.split('.').length - 1,\n          avgWordsPerSentence: 15,\n          complexWords: 5,\n          passiveVoice: 2\n        },\n        plagiarismCheck: enablePlagiarismCheck ? {\n          similarity: 15,\n          sources: similaritySources\n        } : undefined\n      };\n    }, [enablePlagiarismCheck, similaritySources]);\n    \n    // Auto-analyze when content changes\n    useEffect(() => {\n      if (autoAnalyze && debouncedValue.trim()) {\n        analyzeDocument(debouncedValue)\n          .then(analysis => {\n            setLocalSuggestions(analysis.suggestions.slice(0, maxSuggestions));\n            onAnalysisComplete?.(analysis);\n          })\n          .catch(error => {\n            console.error('Analysis failed:', error);\n          });\n      }\n    }, [debouncedValue, autoAnalyze, analyzeDocument, maxSuggestions, onAnalysisComplete]);\n    \n    return (\n      <div className={cn(\"relative\", className)}>\n        <Textarea\n          ref={textareaRef}\n          value={value}\n          onChange={(e) => onChange(e.target.value)}\n          placeholder={placeholder}\n          disabled={disabled}\n          className=\"min-h-[200px] resize-none\"\n          dir={direction}\n        />\n      </div>\n    );\n});\n\nEnhancedDocumentEditor.displayName = 'EnhancedDocumentEditor';\n\nexport default EnhancedDocumentEditor;\n"], "names": [], "mappings": ";;;;AAEA;AAEA;AAGA;AACA;AACA;AACA;AAVA;;;;;;;;AAyHA,qCAAqC;AACrC,MAAM,uCAAyB,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAoD,CAAC,EACzF,KAAK,EACL,QAAQ,EACR,kBAAkB,EAClB,cAAc,gCAAgC,EAC9C,WAAW,KAAK,EAChB,cAAc,IAAI,EAClB,gBAAgB,IAAI,EACpB,cAAc,IAAI,EAClB,wBAAwB,KAAK,EAC7B,qBAAqB,IAAI,EACzB,yBAAyB,IAAI,EAC7B,8BAA8B,IAAI,EAClC,iBAAiB,EAAE,EACnB,WAAW,IAAI,EACf,cAAc,EAAE,EAChB,cAAc,KAAK,EACnB,mBAAmB,CAAC,EACpB,oBAAoB,EAAE,EACtB,oBAAoB,EAAE,EACtB,iBAAiB,EACjB,mBAAmB,EACnB,WAAW,EACX,SAAS,EACT,MAAM,EACN,MAAM,EACN,OAAO,EACP,OAAO,EACP,SAAS,EACV,EAAE;IACD,MAAM,EAAE,CAAC,EAAE,GAAG,CAAA,GAAA,mIAAA,CAAA,UAAO,AAAD;IACpB,MAAM,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,4HAAA,CAAA,WAAQ,AAAD;IACzB,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAuB;IAChD,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA6B;IACxF,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAwB;IAE/E,+BAA+B;IAC/B,MAAM,iBAAiB,CAAA,GAAA,+HAAA,CAAA,cAAW,AAAD,EAAE,OAAO;IAE1C,mFAAmF;IACnF,MAAM,kBAAkB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,OAAO;QACzC,qBAAqB;QACrB,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;QAEjD,wBAAwB;QACxB,MAAM,kBAAwC;YAC5C;gBACE,IAAI;gBACJ,MAAM;gBACN,UAAU;gBACV,SAAS;gBACT,YAAY;gBACZ,iBAAiB;gBACjB,UAAU;gBACV,YAAY;gBACZ,YAAY;gBACZ,UAAU;gBACV,aAAa;YACf;SACD;QAED,OAAO;YACL,aAAa;YACb,SAAS;gBACP,kBAAkB;gBAClB,WAAW,KAAK,KAAK,CAAC,KAAK,MAAM;gBACjC,eAAe,KAAK,KAAK,CAAC,KAAK,MAAM,GAAG;gBACxC,qBAAqB;gBACrB,cAAc;gBACd,cAAc;YAChB;YACA,iBAAiB,wBAAwB;gBACvC,YAAY;gBACZ,SAAS;YACX,IAAI;QACN;IACF,GAAG;QAAC;QAAuB;KAAkB;IAE7C,oCAAoC;IACpC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,eAAe,eAAe,IAAI,IAAI;YACxC,gBAAgB,gBACb,IAAI,CAAC,CAAA;gBACJ,oBAAoB,SAAS,WAAW,CAAC,KAAK,CAAC,GAAG;gBAClD,qBAAqB;YACvB,GACC,KAAK,CAAC,CAAA;gBACL,QAAQ,KAAK,CAAC,oBAAoB;YACpC;QACJ;IACF,GAAG;QAAC;QAAgB;QAAa;QAAiB;QAAgB;KAAmB;IAErF,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;kBAC7B,cAAA,8OAAC,oIAAA,CAAA,WAAQ;YACP,KAAK;YACL,OAAO;YACP,UAAU,CAAC,IAAM,SAAS,EAAE,MAAM,CAAC,KAAK;YACxC,aAAa;YACb,UAAU;YACV,WAAU;YACV,KAAK;;;;;;;;;;;AAIf;AAEA,uBAAuB,WAAW,GAAG;uCAEtB", "debugId": null}}, {"offset": {"line": 5786, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Card = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\n      \"rounded-lg border bg-card text-card-foreground shadow-sm\",\n      className\n    )}\n    {...props}\n  />\n))\nCard.displayName = \"Card\"\n\nconst CardHeader = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex flex-col space-y-1.5 p-6\", className)}\n    {...props}\n  />\n))\nCardHeader.displayName = \"CardHeader\"\n\nconst CardTitle = React.forwardRef<\n  HTMLHeadingElement,\n  React.HTMLAttributes<HTMLHeadingElement>\n>(({ className, ...props }, ref) => (\n  <h3\n    ref={ref}\n    className={cn(\n      \"text-2xl font-semibold leading-none tracking-tight\",\n      className\n    )}\n    {...props}\n  />\n))\nCardTitle.displayName = \"CardTitle\"\n\nconst CardDescription = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLParagraphElement>\n>(({ className, ...props }, ref) => (\n  <p\n    ref={ref}\n    className={cn(\"text-sm text-muted-foreground\", className)}\n    {...props}\n  />\n))\nCardDescription.displayName = \"CardDescription\"\n\nconst CardContent = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"p-6 pt-0\", className)} {...props} />\n))\nCardContent.displayName = \"CardContent\"\n\nconst CardFooter = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex items-center p-6 pt-0\", className)}\n    {...props}\n  />\n))\nCardFooter.displayName = \"CardFooter\"\n\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent }\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AAEA;;;;AAEA,MAAM,qBAAO,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG1B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,4DACA;QAED,GAAG,KAAK;;;;;;AAGb,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,sDACA;QAED,GAAG,KAAK;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAAa,GAAG,KAAK;;;;;;AAEhE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 5867, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/src/components/ui/popover.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as PopoverPrimitive from \"@radix-ui/react-popover\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Popover = PopoverPrimitive.Root\n\nconst PopoverTrigger = PopoverPrimitive.Trigger\n\nconst PopoverContent = React.forwardRef<\n  React.ElementRef<typeof PopoverPrimitive.Content>,\n  React.ComponentPropsWithoutRef<typeof PopoverPrimitive.Content>\n>(({ className, align = \"center\", sideOffset = 4, ...props }, ref) => (\n  <PopoverPrimitive.Portal>\n    <PopoverPrimitive.Content\n      ref={ref}\n      align={align}\n      sideOffset={sideOffset}\n      className={cn(\n        \"z-50 w-72 rounded-md border bg-popover p-4 text-popover-foreground shadow-md outline-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2\",\n        className\n      )}\n      {...props}\n    />\n  </PopoverPrimitive.Portal>\n))\nPopoverContent.displayName = PopoverPrimitive.Content.displayName\n\nexport { Popover, PopoverTrigger, PopoverContent }\n"], "names": [], "mappings": ";;;;;;AAEA;AACA;AAEA;AALA;;;;;AAOA,MAAM,UAAU,mKAAA,CAAA,OAAqB;AAErC,MAAM,iBAAiB,mKAAA,CAAA,UAAwB;AAE/C,MAAM,+BAAiB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGpC,CAAC,EAAE,SAAS,EAAE,QAAQ,QAAQ,EAAE,aAAa,CAAC,EAAE,GAAG,OAAO,EAAE,oBAC5D,8OAAC,mKAAA,CAAA,SAAuB;kBACtB,cAAA,8OAAC,mKAAA,CAAA,UAAwB;YACvB,KAAK;YACL,OAAO;YACP,YAAY;YACZ,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8aACA;YAED,GAAG,KAAK;;;;;;;;;;;AAIf,eAAe,WAAW,GAAG,mKAAA,CAAA,UAAwB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 5908, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/src/components/editor/suggestion-item.tsx"], "sourcesContent": ["\n\"use client\";\n\n// Removed type import - now defined locally\ntype AnalysisSuggestion = {\n  id: string;\n  type: 'style' | 'spelling' | 'grammar' | 'rewrite';\n  message: string;\n  suggestion: string;\n  originalSegment: string;\n  severity: 'low' | 'medium' | 'high';\n  startIndex?: number;\n  endIndex?: number;\n  suggestions?: string[];\n};\nimport { Button } from '@/components/ui/button';\nimport { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';\nimport { Badge } from '@/components/ui/badge';\nimport { Lightbulb, PenTool, SpellCheck, Info, Check, X } from 'lucide-react';\nimport { useI18n } from '@/contexts/i18n-context';\n\ninterface SuggestionItemProps {\n  suggestion: AnalysisSuggestion;\n  onApply: (suggestionText: string, originalSegment: string, startIndex?: number, endIndex?: number) => void;\n  onDismiss: (suggestionId: string) => void;\n}\n\nconst getSuggestionTypeAppearance = (type: AnalysisSuggestion['type']): { color: string; icon: JSX.Element; labelKey: string } => {\n  switch (type) {\n    case 'spelling':\n      return { color: 'bg-red-500 hover:bg-red-600', icon: <SpellCheck className=\"h-3.5 w-3.5 mr-1.5\" />, labelKey: 'suggestionTypeSpelling' };\n    case 'grammar':\n      return { color: 'bg-red-500 hover:bg-red-600', icon: <PenTool className=\"h-3.5 w-3.5 mr-1.5\" />, labelKey: 'suggestionTypeGrammar' };\n    case 'rewrite':\n      return { color: 'bg-blue-500 hover:bg-blue-600', icon: <Lightbulb className=\"h-3.5 w-3.5 mr-1.5\" />, labelKey: 'suggestionTypeRewrite' };\n    case 'style':\n      return { color: 'bg-green-500 hover:bg-green-600', icon: <PenTool className=\"h-3.5 w-3.5 mr-1.5\" />, labelKey: 'suggestionTypeStyle' };\n    default:\n      return { color: 'bg-gray-500 hover:bg-gray-600', icon: <Info className=\"h-3.5 w-3.5 mr-1.5\" />, labelKey: 'suggestionTypeUnknown' };\n  }\n};\n\nexport function SuggestionItem({ suggestion, onApply, onDismiss }: SuggestionItemProps) {\n  const { t } = useI18n();\n  const { color, icon, labelKey } = getSuggestionTypeAppearance(suggestion.type);\n\n  const handleApply = () => {\n    onApply(suggestion.suggestion, suggestion.originalSegment, suggestion.startIndex, suggestion.endIndex);\n  };\n  \n  const handleDismiss = () => {\n      onDismiss(suggestion.id);\n  };\n\n  return (\n    <div className=\"p-3 border-b last:border-b-0 bg-card hover:bg-muted/50 transition-colors\">\n      <div className=\"flex items-start justify-between gap-2\">\n        <div>\n          <div className=\"flex items-center mb-1\">\n            <Badge variant=\"default\" className={`text-xs text-white ${color} mr-2`}>\n              {icon}\n              {t(labelKey)}\n            </Badge>\n             <Popover>\n                <PopoverTrigger asChild>\n                  <Button variant=\"ghost\" size=\"icon\" className=\"h-6 w-6 text-muted-foreground hover:text-foreground\">\n                    <Info className=\"h-3.5 w-3.5\" />\n                    <span className=\"sr-only\">{t('suggestionExplanationTooltip')}</span>\n                  </Button>\n                </PopoverTrigger>\n                <PopoverContent className=\"w-72 text-sm p-3\" side=\"top\" align=\"start\">\n                  {suggestion.message}\n                </PopoverContent>\n              </Popover>\n          </div>\n          <p className=\"text-sm text-muted-foreground mb-1\">\n            <span className=\"italic\">\"{suggestion.originalSegment}\"</span>\n          </p>\n          <p className=\"text-sm font-semibold text-primary\">\n            {t('suggestionLabel')}: <span className=\"font-normal text-foreground\">{suggestion.suggestion}</span>\n          </p>\n        </div>\n        <div className=\"flex items-center gap-1 mt-1 shrink-0\">\n            <Button onClick={handleApply} size=\"sm\" variant=\"outline\" title={t('correctButton')}>\n                <Check className=\"mr-1.5 h-3.5 w-3.5\" />\n                {t('correctButton')}\n            </Button>\n            <Button onClick={handleDismiss} size=\"icon\" variant=\"ghost\" className=\"h-8 w-8\" title={t('dismissButton')}>\n                <X className=\"h-4 w-4\" />\n            </Button>\n        </div>\n      </div>\n    </div>\n  );\n}\n\n    "], "names": [], "mappings": ";;;;AAeA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAlBA;;;;;;;AA0BA,MAAM,8BAA8B,CAAC;IACnC,OAAQ;QACN,KAAK;YACH,OAAO;gBAAE,OAAO;gBAA+B,oBAAM,8OAAC,kNAAA,CAAA,aAAU;oBAAC,WAAU;;;;;;gBAAyB,UAAU;YAAyB;QACzI,KAAK;YACH,OAAO;gBAAE,OAAO;gBAA+B,oBAAM,8OAAC,4MAAA,CAAA,UAAO;oBAAC,WAAU;;;;;;gBAAyB,UAAU;YAAwB;QACrI,KAAK;YACH,OAAO;gBAAE,OAAO;gBAAiC,oBAAM,8OAAC,4MAAA,CAAA,YAAS;oBAAC,WAAU;;;;;;gBAAyB,UAAU;YAAwB;QACzI,KAAK;YACH,OAAO;gBAAE,OAAO;gBAAmC,oBAAM,8OAAC,4MAAA,CAAA,UAAO;oBAAC,WAAU;;;;;;gBAAyB,UAAU;YAAsB;QACvI;YACE,OAAO;gBAAE,OAAO;gBAAiC,oBAAM,8OAAC,kMAAA,CAAA,OAAI;oBAAC,WAAU;;;;;;gBAAyB,UAAU;YAAwB;IACtI;AACF;AAEO,SAAS,eAAe,EAAE,UAAU,EAAE,OAAO,EAAE,SAAS,EAAuB;IACpF,MAAM,EAAE,CAAC,EAAE,GAAG,CAAA,GAAA,mIAAA,CAAA,UAAO,AAAD;IACpB,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,QAAQ,EAAE,GAAG,4BAA4B,WAAW,IAAI;IAE7E,MAAM,cAAc;QAClB,QAAQ,WAAW,UAAU,EAAE,WAAW,eAAe,EAAE,WAAW,UAAU,EAAE,WAAW,QAAQ;IACvG;IAEA,MAAM,gBAAgB;QAClB,UAAU,WAAW,EAAE;IAC3B;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;;sCACC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,iIAAA,CAAA,QAAK;oCAAC,SAAQ;oCAAU,WAAW,CAAC,mBAAmB,EAAE,MAAM,KAAK,CAAC;;wCACnE;wCACA,EAAE;;;;;;;8CAEJ,8OAAC,mIAAA,CAAA,UAAO;;sDACL,8OAAC,mIAAA,CAAA,iBAAc;4CAAC,OAAO;sDACrB,cAAA,8OAAC,kIAAA,CAAA,SAAM;gDAAC,SAAQ;gDAAQ,MAAK;gDAAO,WAAU;;kEAC5C,8OAAC,kMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;kEAChB,8OAAC;wDAAK,WAAU;kEAAW,EAAE;;;;;;;;;;;;;;;;;sDAGjC,8OAAC,mIAAA,CAAA,iBAAc;4CAAC,WAAU;4CAAmB,MAAK;4CAAM,OAAM;sDAC3D,WAAW,OAAO;;;;;;;;;;;;;;;;;;sCAI3B,8OAAC;4BAAE,WAAU;sCACX,cAAA,8OAAC;gCAAK,WAAU;;oCAAS;oCAAE,WAAW,eAAe;oCAAC;;;;;;;;;;;;sCAExD,8OAAC;4BAAE,WAAU;;gCACV,EAAE;gCAAmB;8CAAE,8OAAC;oCAAK,WAAU;8CAA+B,WAAW,UAAU;;;;;;;;;;;;;;;;;;8BAGhG,8OAAC;oBAAI,WAAU;;sCACX,8OAAC,kIAAA,CAAA,SAAM;4BAAC,SAAS;4BAAa,MAAK;4BAAK,SAAQ;4BAAU,OAAO,EAAE;;8CAC/D,8OAAC,oMAAA,CAAA,QAAK;oCAAC,WAAU;;;;;;gCAChB,EAAE;;;;;;;sCAEP,8OAAC,kIAAA,CAAA,SAAM;4BAAC,SAAS;4BAAe,MAAK;4BAAO,SAAQ;4BAAQ,WAAU;4BAAU,OAAO,EAAE;sCACrF,cAAA,8OAAC,4LAAA,CAAA,IAAC;gCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAM7B", "debugId": null}}, {"offset": {"line": 6190, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/src/components/editor/writing-suggestions-panel.tsx"], "sourcesContent": ["\n\"use client\";\n\nimport { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardDescription } from \"@/components/ui/card\";\nimport { Loader2, CheckCircle, AlertTriangle } from \"lucide-react\";\n// Removed type import - now defined locally\ntype AnalysisSuggestion = {\n  id: string;\n  type: 'style' | 'spelling' | 'grammar' | 'rewrite';\n  message: string;\n  suggestion: string;\n  originalSegment: string;\n  severity: 'low' | 'medium' | 'high';\n  startIndex?: number;\n  endIndex?: number;\n  suggestions?: string[];\n};\nimport { SuggestionItem } from './suggestion-item';\nimport { useI18n } from '@/contexts/i18n-context';\nimport { ScrollArea } from '@/components/ui/scroll-area';\nimport React from 'react';\n\ninterface WritingSuggestionsPanelProps {\n  suggestions: AnalysisSuggestion[];\n  isAnalyzing: boolean;\n  onApplySuggestion: (\n    suggestionText: string,\n    originalSegment: string,\n    startIndex?: number,\n    endIndex?: number\n  ) => void;\n  onDismissSuggestion: (suggestionId: string) => void;\n}\n\nexport function WritingSuggestionsPanel({ \n  suggestions, \n  isAnalyzing, \n  onApplySuggestion, \n  onDismissSuggestion\n}: WritingSuggestionsPanelProps) {\n  const { t } = useI18n();\n\n  const hasSuggestions = suggestions.length > 0;\n  \n  const descriptionKey = isAnalyzing \n    ? 'analyzingTextDescription' \n    : hasSuggestions \n    ? 'suggestionsFoundDescription' \n    : 'startTypingForSuggestionsDescription';\n\n  return (\n    <Card className=\"h-full flex flex-col\">\n      <CardHeader className=\"p-3 border-b sticky top-0 bg-card z-10\">\n        <CardTitle className=\"text-base flex items-center\">\n            {isAnalyzing && <Loader2 className=\"h-3.5 w-3.5 animate-spin mr-2\" />}\n            {!isAnalyzing && hasSuggestions && <CheckCircle className=\"h-3.5 w-3.5 text-primary mr-2\" />}\n            {!isAnalyzing && !hasSuggestions && <AlertTriangle className=\"h-3.5 w-3.5 text-muted-foreground mr-2\" />}\n            {t('writingSuggestionsTitle')}\n        </CardTitle>\n        <CardDescription className=\"text-xs\">\n            {t(descriptionKey, { count: suggestions.length.toString() })}\n        </CardDescription>\n      </CardHeader>\n      \n      <CardContent className=\"flex-grow p-0 relative\">\n        <div className=\"absolute inset-0\">\n          <ScrollArea className=\"h-full w-full\">\n            <div className=\"p-1\">\n              {hasSuggestions ? (\n                suggestions.map((suggestion) => (\n                  <SuggestionItem\n                    key={suggestion.id}\n                    suggestion={suggestion}\n                    onApply={onApplySuggestion}\n                    onDismiss={onDismissSuggestion}\n                  />\n                ))\n              ) : !isAnalyzing ? (\n                <p className=\"p-4 text-sm text-muted-foreground text-center\">{t('startTypingForSuggestionsDescription')}</p>\n              ) : null}\n            </div>\n          </ScrollArea>\n        </div>\n      </CardContent>\n\n      {hasSuggestions && (\n        <CardFooter className=\"p-2 border-t text-xs text-muted-foreground justify-center sticky bottom-0 bg-card z-10\">\n          {t('suggestionsFoundDescription', { count: suggestions.length.toString() })}\n        </CardFooter>\n      )}\n    </Card>\n  );\n}\n"], "names": [], "mappings": ";;;;AAGA;AACA;AAAA;AAAA;AAaA;AACA;AACA;AAlBA;;;;;;;AAiCO,SAAS,wBAAwB,EACtC,WAAW,EACX,WAAW,EACX,iBAAiB,EACjB,mBAAmB,EACU;IAC7B,MAAM,EAAE,CAAC,EAAE,GAAG,CAAA,GAAA,mIAAA,CAAA,UAAO,AAAD;IAEpB,MAAM,iBAAiB,YAAY,MAAM,GAAG;IAE5C,MAAM,iBAAiB,cACnB,6BACA,iBACA,gCACA;IAEJ,qBACE,8OAAC,gIAAA,CAAA,OAAI;QAAC,WAAU;;0BACd,8OAAC,gIAAA,CAAA,aAAU;gBAAC,WAAU;;kCACpB,8OAAC,gIAAA,CAAA,YAAS;wBAAC,WAAU;;4BAChB,6BAAe,8OAAC,iNAAA,CAAA,UAAO;gCAAC,WAAU;;;;;;4BAClC,CAAC,eAAe,gCAAkB,8OAAC,2NAAA,CAAA,cAAW;gCAAC,WAAU;;;;;;4BACzD,CAAC,eAAe,CAAC,gCAAkB,8OAAC,wNAAA,CAAA,gBAAa;gCAAC,WAAU;;;;;;4BAC5D,EAAE;;;;;;;kCAEP,8OAAC,gIAAA,CAAA,kBAAe;wBAAC,WAAU;kCACtB,EAAE,gBAAgB;4BAAE,OAAO,YAAY,MAAM,CAAC,QAAQ;wBAAG;;;;;;;;;;;;0BAIhE,8OAAC,gIAAA,CAAA,cAAW;gBAAC,WAAU;0BACrB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC,0IAAA,CAAA,aAAU;wBAAC,WAAU;kCACpB,cAAA,8OAAC;4BAAI,WAAU;sCACZ,iBACC,YAAY,GAAG,CAAC,CAAC,2BACf,8OAAC,kJAAA,CAAA,iBAAc;oCAEb,YAAY;oCACZ,SAAS;oCACT,WAAW;mCAHN,WAAW,EAAE;;;;4CAMpB,CAAC,4BACH,8OAAC;gCAAE,WAAU;0CAAiD,EAAE;;;;;uCAC9D;;;;;;;;;;;;;;;;;;;;;YAMX,gCACC,8OAAC,gIAAA,CAAA,aAAU;gBAAC,WAAU;0BACnB,EAAE,+BAA+B;oBAAE,OAAO,YAAY,MAAM,CAAC,QAAQ;gBAAG;;;;;;;;;;;;AAKnF", "debugId": null}}, {"offset": {"line": 6332, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/src/components/editor/word-toolkit-popover.tsx"], "sourcesContent": ["\n\"use client\";\n\nimport { useState, useEffect, useCallback, useRef } from 'react';\nimport { PopoverContent } from '@/components/ui/popover';\nimport { Badge } from '@/components/ui/badge';\nimport { Button } from \"@/components/ui/button\";\nimport { Loader2, Volume2 } from 'lucide-react';\nimport { useI18n } from '@/contexts/i18n-context';\nimport { useToast } from '@/hooks/use-toast';\n// Import the correct types from the server flows\nimport type { WordToolkitOutput, WordToolkitInput } from '@/ai/flows/word-toolkit-flow';\nimport type { SpelledOutAudioInput } from '@/ai/flows/text-to-speech-flow';\n\ninterface WordToolkitPopoverProps {\n  selectedWord: string;\n  contextText: string;\n  language: string;\n  onSynonymSelect: (synonym: string) => void;\n}\n\nexport function WordToolkitPopover({ selectedWord, contextText, language, onSynonymSelect }: WordToolkitPopoverProps) {\n  const { t } = useI18n();\n  const { toast } = useToast();\n  const [isLoading, setIsLoading] = useState(true);\n  const [analysisResult, setAnalysisResult] = useState<WordToolkitOutput | null>(null);\n  const [isPronouncing, setIsPronouncing] = useState(false);\n  const audioRef = useRef<HTMLAudioElement | null>(null);\n\n  const handleWordAnalysis = useCallback(async () => {\n    setIsLoading(true);\n    setAnalysisResult(null);\n    try {\n      const input: WordToolkitInput = {\n        word: selectedWord,\n        context: contextText,\n        language,\n      };\n      const response = await fetch('/api/ai/word-suggestions', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify(input),\n      });\n\n      if (!response.ok) {\n        throw new Error(`HTTP error! status: ${response.status}`);\n      }\n\n      const result = await response.json();\n      setAnalysisResult(result);\n    } catch (error) {\n      console.error(\"Error in word toolkit analysis:\", error);\n      toast({ titleKey: \"toastErrorTitle\", descriptionKey: \"toastWordToolkitError\", variant: \"destructive\" });\n    } finally {\n      setIsLoading(false);\n    }\n  }, [selectedWord, contextText, language, toast]);\n\n  useEffect(() => {\n    if (selectedWord) {\n        handleWordAnalysis();\n    }\n  // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [selectedWord, contextText, language]);\n\n  const handlePronounce = async () => {\n    if (!analysisResult?.correctSpelling) return;\n    setIsPronouncing(true);\n    try {\n      const input: SpelledOutAudioInput = {\n        word: analysisResult.correctSpelling,\n        lang: language,\n      };\n      const response = await fetch('/api/ai/text-to-speech', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify(input),\n      });\n\n      if (!response.ok) {\n        throw new Error(`HTTP error! status: ${response.status}`);\n      }\n\n      const result = await response.json();\n      const { audioDataUri } = result;\n\n      if (audioDataUri && audioRef.current) {\n        audioRef.current.src = audioDataUri;\n        audioRef.current.play();\n      } else {\n        toast({ titleKey: \"toastErrorTitle\", descriptionKey: \"toastPronunciationError\", variant: \"destructive\" });\n      }\n    } catch (error) {\n      console.error(\"Error generating or playing pronunciation:\", error);\n      toast({ titleKey: \"toastErrorTitle\", descriptionKey: \"toastPronunciationError\", variant: \"destructive\" });\n    } finally {\n      setIsPronouncing(false);\n    }\n  };\n\n  return (\n    <PopoverContent className=\"w-80\" side=\"top\" align=\"start\">\n      <div className=\"grid gap-4\">\n        <div className=\"space-y-2\">\n          <h4 className=\"font-medium leading-none\">{t('wordToolkitTitle')}</h4>\n          <p className=\"text-sm text-muted-foreground\">{t('wordToolkitPopoverDescription')}</p>\n        </div>\n\n        {isLoading && (\n          <div className=\"flex items-center justify-center h-24\">\n            <Loader2 className=\"h-5 w-5 animate-spin text-primary\" />\n          </div>\n        )}\n\n        {!isLoading && analysisResult && (\n          <div className=\"space-y-4\">\n            <div>\n              <h5 className=\"text-sm font-semibold mb-2\">{t('synonymsLabel')}</h5>\n              <div className=\"flex flex-wrap gap-2\">\n                {analysisResult.synonyms.length > 0 ? (\n                  analysisResult.synonyms.map((synonym) => (\n                    <Badge\n                      key={synonym}\n                      variant=\"secondary\"\n                      className=\"cursor-pointer hover:bg-primary hover:text-primary-foreground\"\n                      onClick={() => onSynonymSelect(synonym)}\n                      title={t('applySynonymTooltip', { synonym })}\n                    >\n                      {synonym}\n                    </Badge>\n                  ))\n                ) : (\n                  <p className=\"text-xs text-muted-foreground\">{t('noSynonymsFound')}</p>\n                )}\n              </div>\n            </div>\n\n            <div>\n              <h5 className=\"text-sm font-semibold mb-2\">{t('pronunciationLabel')}</h5>\n              <div className=\"flex items-center justify-between gap-2 p-2 bg-muted rounded-md\">\n                <span className=\"text-sm font-semibold\">{analysisResult.correctSpelling}</span>\n                <Button size=\"icon\" variant=\"ghost\" className=\"h-7 w-7\" onClick={handlePronounce} disabled={isPronouncing} title={t('pronounceButton')}>\n                  {isPronouncing ? <Loader2 className=\"h-4 w-4 animate-spin\" /> : <Volume2 className=\"h-4 w-4\" />}\n                </Button>\n              </div>\n            </div>\n          </div>\n        )}\n      </div>\n      <audio ref={audioRef} className=\"hidden\" onEnded={() => setIsPronouncing(false)} onError={() => setIsPronouncing(false)}/>\n    </PopoverContent>\n  );\n}\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AARA;;;;;;;;;AAoBO,SAAS,mBAAmB,EAAE,YAAY,EAAE,WAAW,EAAE,QAAQ,EAAE,eAAe,EAA2B;IAClH,MAAM,EAAE,CAAC,EAAE,GAAG,CAAA,GAAA,mIAAA,CAAA,UAAO,AAAD;IACpB,MAAM,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,4HAAA,CAAA,WAAQ,AAAD;IACzB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA4B;IAC/E,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAA2B;IAEjD,MAAM,qBAAqB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACrC,aAAa;QACb,kBAAkB;QAClB,IAAI;YACF,MAAM,QAA0B;gBAC9B,MAAM;gBACN,SAAS;gBACT;YACF;YACA,MAAM,WAAW,MAAM,MAAM,4BAA4B;gBACvD,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;YACvB;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;YAC1D;YAEA,MAAM,SAAS,MAAM,SAAS,IAAI;YAClC,kBAAkB;QACpB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,mCAAmC;YACjD,MAAM;gBAAE,UAAU;gBAAmB,gBAAgB;gBAAyB,SAAS;YAAc;QACvG,SAAU;YACR,aAAa;QACf;IACF,GAAG;QAAC;QAAc;QAAa;QAAU;KAAM;IAE/C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,cAAc;YACd;QACJ;IACF,uDAAuD;IACvD,GAAG;QAAC;QAAc;QAAa;KAAS;IAExC,MAAM,kBAAkB;QACtB,IAAI,CAAC,gBAAgB,iBAAiB;QACtC,iBAAiB;QACjB,IAAI;YACF,MAAM,QAA8B;gBAClC,MAAM,eAAe,eAAe;gBACpC,MAAM;YACR;YACA,MAAM,WAAW,MAAM,MAAM,0BAA0B;gBACrD,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;YACvB;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;YAC1D;YAEA,MAAM,SAAS,MAAM,SAAS,IAAI;YAClC,MAAM,EAAE,YAAY,EAAE,GAAG;YAEzB,IAAI,gBAAgB,SAAS,OAAO,EAAE;gBACpC,SAAS,OAAO,CAAC,GAAG,GAAG;gBACvB,SAAS,OAAO,CAAC,IAAI;YACvB,OAAO;gBACL,MAAM;oBAAE,UAAU;oBAAmB,gBAAgB;oBAA2B,SAAS;gBAAc;YACzG;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,8CAA8C;YAC5D,MAAM;gBAAE,UAAU;gBAAmB,gBAAgB;gBAA2B,SAAS;YAAc;QACzG,SAAU;YACR,iBAAiB;QACnB;IACF;IAEA,qBACE,8OAAC,mIAAA,CAAA,iBAAc;QAAC,WAAU;QAAO,MAAK;QAAM,OAAM;;0BAChD,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAA4B,EAAE;;;;;;0CAC5C,8OAAC;gCAAE,WAAU;0CAAiC,EAAE;;;;;;;;;;;;oBAGjD,2BACC,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,iNAAA,CAAA,UAAO;4BAAC,WAAU;;;;;;;;;;;oBAItB,CAAC,aAAa,gCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;;kDACC,8OAAC;wCAAG,WAAU;kDAA8B,EAAE;;;;;;kDAC9C,8OAAC;wCAAI,WAAU;kDACZ,eAAe,QAAQ,CAAC,MAAM,GAAG,IAChC,eAAe,QAAQ,CAAC,GAAG,CAAC,CAAC,wBAC3B,8OAAC,iIAAA,CAAA,QAAK;gDAEJ,SAAQ;gDACR,WAAU;gDACV,SAAS,IAAM,gBAAgB;gDAC/B,OAAO,EAAE,uBAAuB;oDAAE;gDAAQ;0DAEzC;+CANI;;;;sEAUT,8OAAC;4CAAE,WAAU;sDAAiC,EAAE;;;;;;;;;;;;;;;;;0CAKtD,8OAAC;;kDACC,8OAAC;wCAAG,WAAU;kDAA8B,EAAE;;;;;;kDAC9C,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAK,WAAU;0DAAyB,eAAe,eAAe;;;;;;0DACvE,8OAAC,kIAAA,CAAA,SAAM;gDAAC,MAAK;gDAAO,SAAQ;gDAAQ,WAAU;gDAAU,SAAS;gDAAiB,UAAU;gDAAe,OAAO,EAAE;0DACjH,8BAAgB,8OAAC,iNAAA,CAAA,UAAO;oDAAC,WAAU;;;;;yEAA4B,8OAAC,4MAAA,CAAA,UAAO;oDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAO/F,8OAAC;gBAAM,KAAK;gBAAU,WAAU;gBAAS,SAAS,IAAM,iBAAiB;gBAAQ,SAAS,IAAM,iBAAiB;;;;;;;;;;;;AAGvH", "debugId": null}}, {"offset": {"line": 6634, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 6671, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/src/components/ai/ai-tone-analyzer.tsx"], "sourcesContent": ["\n\"use client\";\n\nimport { useState, type FormEvent } from 'react';\nimport { But<PERSON> } from \"@/components/ui/button\";\nimport { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>er, CardTitle } from \"@/components/ui/card\";\nimport { Gauge, Loader2 } from \"lucide-react\";\n// Removed direct import of server action\ntype AnalyzeToneOutput = {\n  tone: string;\n  confidence: number;\n  reasoning: string;\n};\nimport { useToast } from '@/hooks/use-toast';\nimport { useI18n } from '@/contexts/i18n-context';\n\ninterface AiToneAnalyzerProps {\n  currentText: string;\n}\n\nexport function AiToneAnalyzer({ currentText }: AiToneAnalyzerProps) {\n  const [analysisResult, setAnalysisResult] = useState<AnalyzeToneOutput | null>(null);\n  const [isLoading, setIsLoading] = useState(false);\n  const { toast } = useToast();\n  const { t } = useI18n();\n\n  const handleSubmit = async (event: FormEvent<HTMLFormElement>) => {\n    event.preventDefault();\n    if (!currentText.trim()) {\n      toast({ titleKey: \"toastInputRequiredTitle\", descriptionKey: \"toastEditorEmptyError\", variant: \"destructive\" });\n      return;\n    }\n    setIsLoading(true);\n    setAnalysisResult(null);\n\n    try {\n      const response = await fetch('/api/ai/analyze-tone', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({ text: currentText }),\n      });\n\n      if (!response.ok) {\n        throw new Error(`HTTP error! status: ${response.status}`);\n      }\n\n      const result = await response.json();\n      if (result) {\n        setAnalysisResult(result);\n        toast({ titleKey: \"toastSuccessTitle\", descriptionKey: \"toastToneAnalysisSuccess\" });\n      } else {\n        throw new Error(\"Tone analysis returned no result.\");\n      }\n    } catch (error) {\n      console.error(\"Error analyzing tone:\", error);\n      toast({ titleKey: \"toastErrorTitle\", descriptionKey: \"toastToneAnalysisError\", variant: \"destructive\" });\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  return (\n    <Card className=\"border-none shadow-none\">\n      <CardHeader className=\"p-0 pb-4\">\n        <CardTitle className=\"text-base\">{t('aiToneAnalysisDescription')}</CardTitle>\n      </CardHeader>\n      <form onSubmit={handleSubmit}>\n        <CardContent className=\"space-y-4 p-0\">\n          {analysisResult && (\n            <div className=\"space-y-3 p-3 bg-muted rounded-md\">\n              <div>\n                <h4 className=\"font-semibold text-sm\">{t('formalityLabel')}:</h4>\n                <p className=\"text-sm text-muted-foreground\">{analysisResult.formality}</p>\n              </div>\n              <div>\n                <h4 className=\"font-semibold text-sm\">{t('confidenceLabel')}:</h4>\n                <p className=\"text-sm text-muted-foreground\">{analysisResult.confidence}</p>\n              </div>\n              <div>\n                <h4 className=\"font-semibold text-sm\">{t('feedbackLabel')}:</h4>\n                <p className=\"text-sm text-muted-foreground\">{analysisResult.feedback}</p>\n              </div>\n            </div>\n          )}\n          {!isLoading && !analysisResult && currentText.trim().length === 0 && (\n            <p className=\"text-sm text-muted-foreground text-center py-8\">{t('writeSomeTextToAnalyzePlaceholder')}</p>\n          )}\n           {!isLoading && !analysisResult && currentText.trim().length > 0 && (\n             <p className=\"text-sm text-muted-foreground text-center py-8\">{t('Click the button to analyze the tone.')}</p>\n          )}\n        </CardContent>\n        <CardFooter className=\"p-0 pt-4\">\n          <Button type=\"submit\" disabled={isLoading || currentText.length === 0} className=\"w-full\">\n            {isLoading ? <Loader2 className=\"mr-2 h-4 w-4 animate-spin\" /> : <Gauge className=\"mr-2 h-4 w-4\" />}\n            {t('analyzeToneButton')}\n          </Button>\n        </CardFooter>\n      </form>\n    </Card>\n  );\n}\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AACA;AAAA;AAOA;AACA;AAbA;;;;;;;;AAmBO,SAAS,eAAe,EAAE,WAAW,EAAuB;IACjE,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA4B;IAC/E,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,4HAAA,CAAA,WAAQ,AAAD;IACzB,MAAM,EAAE,CAAC,EAAE,GAAG,CAAA,GAAA,mIAAA,CAAA,UAAO,AAAD;IAEpB,MAAM,eAAe,OAAO;QAC1B,MAAM,cAAc;QACpB,IAAI,CAAC,YAAY,IAAI,IAAI;YACvB,MAAM;gBAAE,UAAU;gBAA2B,gBAAgB;gBAAyB,SAAS;YAAc;YAC7G;QACF;QACA,aAAa;QACb,kBAAkB;QAElB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,wBAAwB;gBACnD,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBAAE,MAAM;gBAAY;YAC3C;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;YAC1D;YAEA,MAAM,SAAS,MAAM,SAAS,IAAI;YAClC,IAAI,QAAQ;gBACV,kBAAkB;gBAClB,MAAM;oBAAE,UAAU;oBAAqB,gBAAgB;gBAA2B;YACpF,OAAO;gBACL,MAAM,IAAI,MAAM;YAClB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yBAAyB;YACvC,MAAM;gBAAE,UAAU;gBAAmB,gBAAgB;gBAA0B,SAAS;YAAc;QACxG,SAAU;YACR,aAAa;QACf;IACF;IAEA,qBACE,8OAAC,gIAAA,CAAA,OAAI;QAAC,WAAU;;0BACd,8OAAC,gIAAA,CAAA,aAAU;gBAAC,WAAU;0BACpB,cAAA,8OAAC,gIAAA,CAAA,YAAS;oBAAC,WAAU;8BAAa,EAAE;;;;;;;;;;;0BAEtC,8OAAC;gBAAK,UAAU;;kCACd,8OAAC,gIAAA,CAAA,cAAW;wBAAC,WAAU;;4BACpB,gCACC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;;0DACC,8OAAC;gDAAG,WAAU;;oDAAyB,EAAE;oDAAkB;;;;;;;0DAC3D,8OAAC;gDAAE,WAAU;0DAAiC,eAAe,SAAS;;;;;;;;;;;;kDAExE,8OAAC;;0DACC,8OAAC;gDAAG,WAAU;;oDAAyB,EAAE;oDAAmB;;;;;;;0DAC5D,8OAAC;gDAAE,WAAU;0DAAiC,eAAe,UAAU;;;;;;;;;;;;kDAEzE,8OAAC;;0DACC,8OAAC;gDAAG,WAAU;;oDAAyB,EAAE;oDAAiB;;;;;;;0DAC1D,8OAAC;gDAAE,WAAU;0DAAiC,eAAe,QAAQ;;;;;;;;;;;;;;;;;;4BAI1E,CAAC,aAAa,CAAC,kBAAkB,YAAY,IAAI,GAAG,MAAM,KAAK,mBAC9D,8OAAC;gCAAE,WAAU;0CAAkD,EAAE;;;;;;4BAEjE,CAAC,aAAa,CAAC,kBAAkB,YAAY,IAAI,GAAG,MAAM,GAAG,mBAC5D,8OAAC;gCAAE,WAAU;0CAAkD,EAAE;;;;;;;;;;;;kCAGtE,8OAAC,gIAAA,CAAA,aAAU;wBAAC,WAAU;kCACpB,cAAA,8OAAC,kIAAA,CAAA,SAAM;4BAAC,MAAK;4BAAS,UAAU,aAAa,YAAY,MAAM,KAAK;4BAAG,WAAU;;gCAC9E,0BAAY,8OAAC,iNAAA,CAAA,UAAO;oCAAC,WAAU;;;;;yDAAiC,8OAAC,oMAAA,CAAA,QAAK;oCAAC,WAAU;;;;;;gCACjF,EAAE;;;;;;;;;;;;;;;;;;;;;;;;AAMf", "debugId": null}}, {"offset": {"line": 6928, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/src/components/ai/ai-text-generator.tsx"], "sourcesContent": ["\n\"use client\";\n\nimport { useState, type FormEvent } from 'react';\nimport { But<PERSON> } from \"@/components/ui/button\";\nimport { Textarea } from \"@/components/ui/textarea\";\nimport { Input } from \"@/components/ui/input\";\nimport { Label } from \"@/components/ui/label\";\nimport { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, CardHeader, CardTitle } from \"@/components/ui/card\";\nimport { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from \"@/components/ui/accordion\";\nimport { Sparkles, Loader2, Wand2, History, Copy, ClipboardEdit, ClipboardPaste } from \"lucide-react\";\n// Removed direct import of server action\nimport { useToast } from '@/hooks/use-toast';\nimport { useI18n } from '@/contexts/i18n-context';\n\ninterface GenerationHistoryItem {\n  id: string;\n  prompt: string;\n  generatedText: string;\n}\n\ninterface AiTextGeneratorProps {\n  onInsertText: (text: string) => void;\n}\n\nconst MAX_HISTORY_ITEMS = 10;\n\nexport function AiTextGenerator({ onInsertText }: AiTextGeneratorProps) {\n  const [prompt, setPrompt] = useState(\"\");\n  const [generatedOutput, setGeneratedOutput] = useState(\"\");\n  const [isLoading, setIsLoading] = useState(false);\n  const [generationHistory, setGenerationHistory] = useState<GenerationHistoryItem[]>([]);\n  const { toast } = useToast();\n  const { t } = useI18n();\n\n  const handleSubmit = async (event: FormEvent<HTMLFormElement>) => {\n    event.preventDefault();\n    const currentPrompt = prompt.trim();\n    if (!currentPrompt) {\n      toast({ titleKey: \"toastInputRequiredTitle\", descriptionKey: \"toastPromptRequiredError\", variant: \"destructive\" });\n      return;\n    }\n    setIsLoading(true);\n    setGeneratedOutput(\"\");\n\n    try {\n      const response = await fetch('/api/ai/generate-text', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({ prompt: currentPrompt }),\n      });\n\n      if (!response.ok) {\n        throw new Error(`HTTP error! status: ${response.status}`);\n      }\n\n      const result = await response.json();\n      if (result && result.generatedText) {\n        setGeneratedOutput(result.generatedText);\n        setGenerationHistory(prev => \n          [{ id: Date.now().toString(), prompt: currentPrompt, generatedText: result.generatedText }, ...prev].slice(0, MAX_HISTORY_ITEMS)\n        );\n        toast({ titleKey: \"toastSuccessTitle\", descriptionKey: \"toastTextGeneratedSuccess\" });\n      } else {\n        throw new Error(\"AI did not return valid generated text.\");\n      }\n    } catch (error) {\n      console.error(\"Error generating text:\", error);\n      toast({ titleKey: \"toastErrorTitle\", descriptionKey: \"toastTextGenerationError\", variant: \"destructive\" });\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const handleUsePrompt = (promptToUse: string) => {\n    setPrompt(promptToUse);\n    toast({ titleKey: \"toastInfoTitle\", descriptionKey: \"toastPromptRestoredSuccess\" });\n  };\n\n  const handleCopyOutput = async (textToCopy: string) => {\n    try {\n      await navigator.clipboard.writeText(textToCopy);\n      toast({ titleKey: \"toastSuccessTitle\", descriptionKey: \"toastTextCopiedSuccess\" });\n    } catch (error) {\n      console.error(\"Failed to copy text:\", error);\n      toast({ titleKey: \"toastErrorTitle\", descriptionKey: \"toastTextCopyError\", variant: \"destructive\" });\n    }\n  };\n\n  const handleInsertIntoEditor = () => {\n    if (generatedOutput) {\n      onInsertText(generatedOutput);\n      toast({ titleKey: \"toastSuccessTitle\", descriptionKey: \"toastTextInsertedSuccess\" });\n    }\n  };\n\n  return (\n    <Card className=\"border-none shadow-none\">\n       <CardHeader className=\"p-0 pb-4\">\n        <CardTitle className=\"text-base\">{t('aiTextGenerationDescription')}</CardTitle>\n      </CardHeader>\n      <CardContent className=\"p-0\">\n        <form onSubmit={handleSubmit} className=\"space-y-4\">\n          <div>\n            <Label htmlFor=\"generation-prompt\" className=\"text-xs text-muted-foreground\">{t('yourPromptLabel')}</Label>\n            <Input\n              id=\"generation-prompt\"\n              value={prompt}\n              onChange={(e) => setPrompt(e.target.value)}\n              placeholder={t('promptPlaceholder')}\n              className=\"mt-1\"\n              disabled={isLoading}\n            />\n          </div>\n          {generatedOutput && (\n            <div>\n              <Label htmlFor=\"generated-text-output\" className=\"text-xs text-muted-foreground\">{t('generatedTextLabel')}</Label>\n              <Textarea\n                id=\"generated-text-output\"\n                value={generatedOutput}\n                readOnly\n                className=\"mt-1 min-h-[120px] bg-muted font-code\"\n              />\n               <div className=\"mt-2 flex justify-end\">\n                <Button \n                  type=\"button\" \n                  variant=\"outline\" \n                  size=\"sm\" \n                  onClick={handleInsertIntoEditor} \n                  disabled={isLoading || !generatedOutput}\n                  title={t('insertIntoEditorButtonTooltip')}\n                >\n                  <ClipboardPaste className=\"mr-2 h-3.5 w-3.5\" />\n                  {t('insertIntoEditorButton')}\n                </Button>\n              </div>\n            </div>\n          )}\n          <Button type=\"submit\" disabled={isLoading} className=\"w-full\">\n            {isLoading ? <Loader2 className=\"mr-2 h-4 w-4 animate-spin\" /> : <Sparkles className=\"mr-2 h-4 w-4\" />}\n            {t('generateTextButton')}\n          </Button>\n        </form>\n\n        {generationHistory.length > 0 && (\n            <Accordion type=\"single\" collapsible className=\"w-full mt-6\">\n            <AccordionItem value=\"generation-history\">\n                <AccordionTrigger className=\"text-sm font-medium\">\n                <div className=\"flex items-center\">\n                    <History className=\"mr-2 h-4 w-4\" />\n                    {t('generationHistoryTitle')} \n                </div>\n                </AccordionTrigger>\n                <AccordionContent className=\"px-1 pb-2 pt-0\">\n                <div className=\"space-y-3 max-h-80 overflow-y-auto\">\n                    {generationHistory.map(item => (\n                    <Card key={item.id} className=\"bg-muted/50\">\n                        <CardHeader className=\"p-3\">\n                        <Label className=\"text-xs text-muted-foreground\">{t('promptLabel')}</Label>\n                        <p className=\"text-xs font-code line-clamp-2\">{item.prompt}</p>\n                        </CardHeader>\n                        <CardContent className=\"p-3 pt-0\">\n                        <Label className=\"text-xs text-muted-foreground\">{t('outputLabel')}</Label>\n                        <Textarea\n                            value={item.generatedText}\n                            readOnly\n                            className=\"mt-1 h-20 text-xs font-code bg-background\"\n                        />\n                        </CardContent>\n                        <CardFooter className=\"p-3 pt-0 flex justify-end gap-2\">\n                        <Button \n                            variant=\"outline\" \n                            size=\"sm\" \n                            onClick={() => { onInsertText(item.generatedText); toast({ titleKey: \"toastSuccessTitle\", descriptionKey: \"toastTextInsertedSuccess\" });}}\n                            title={t('insertIntoEditorButtonTooltip')}\n                        >\n                            <ClipboardPaste className=\"h-3.5 w-3.5\" />\n                        </Button>\n                        <Button variant=\"outline\" size=\"sm\" onClick={() => handleUsePrompt(item.prompt)} title={t('useThisPromptButton')}>\n                            <ClipboardEdit className=\"h-3.5 w-3.5\" />\n                        </Button>\n                        <Button variant=\"outline\" size=\"sm\" onClick={() => handleCopyOutput(item.generatedText)} title={t('copyOutputButton')}>\n                            <Copy className=\"h-3.5 w-3.5\" />\n                        </Button>\n                        </CardFooter>\n                    </Card>\n                    ))}\n                </div>\n                </AccordionContent>\n            </AccordionItem>\n            </Accordion>\n        )}\n      </CardContent>\n    </Card>\n  );\n}\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA,yCAAyC;AACzC;AACA;AAZA;;;;;;;;;;;;AAwBA,MAAM,oBAAoB;AAEnB,SAAS,gBAAgB,EAAE,YAAY,EAAwB;IACpE,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA2B,EAAE;IACtF,MAAM,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,4HAAA,CAAA,WAAQ,AAAD;IACzB,MAAM,EAAE,CAAC,EAAE,GAAG,CAAA,GAAA,mIAAA,CAAA,UAAO,AAAD;IAEpB,MAAM,eAAe,OAAO;QAC1B,MAAM,cAAc;QACpB,MAAM,gBAAgB,OAAO,IAAI;QACjC,IAAI,CAAC,eAAe;YAClB,MAAM;gBAAE,UAAU;gBAA2B,gBAAgB;gBAA4B,SAAS;YAAc;YAChH;QACF;QACA,aAAa;QACb,mBAAmB;QAEnB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,yBAAyB;gBACpD,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBAAE,QAAQ;gBAAc;YAC/C;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;YAC1D;YAEA,MAAM,SAAS,MAAM,SAAS,IAAI;YAClC,IAAI,UAAU,OAAO,aAAa,EAAE;gBAClC,mBAAmB,OAAO,aAAa;gBACvC,qBAAqB,CAAA,OACnB;wBAAC;4BAAE,IAAI,KAAK,GAAG,GAAG,QAAQ;4BAAI,QAAQ;4BAAe,eAAe,OAAO,aAAa;wBAAC;2BAAM;qBAAK,CAAC,KAAK,CAAC,GAAG;gBAEhH,MAAM;oBAAE,UAAU;oBAAqB,gBAAgB;gBAA4B;YACrF,OAAO;gBACL,MAAM,IAAI,MAAM;YAClB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;YACxC,MAAM;gBAAE,UAAU;gBAAmB,gBAAgB;gBAA4B,SAAS;YAAc;QAC1G,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,kBAAkB,CAAC;QACvB,UAAU;QACV,MAAM;YAAE,UAAU;YAAkB,gBAAgB;QAA6B;IACnF;IAEA,MAAM,mBAAmB,OAAO;QAC9B,IAAI;YACF,MAAM,UAAU,SAAS,CAAC,SAAS,CAAC;YACpC,MAAM;gBAAE,UAAU;gBAAqB,gBAAgB;YAAyB;QAClF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,wBAAwB;YACtC,MAAM;gBAAE,UAAU;gBAAmB,gBAAgB;gBAAsB,SAAS;YAAc;QACpG;IACF;IAEA,MAAM,yBAAyB;QAC7B,IAAI,iBAAiB;YACnB,aAAa;YACb,MAAM;gBAAE,UAAU;gBAAqB,gBAAgB;YAA2B;QACpF;IACF;IAEA,qBACE,8OAAC,gIAAA,CAAA,OAAI;QAAC,WAAU;;0BACb,8OAAC,gIAAA,CAAA,aAAU;gBAAC,WAAU;0BACrB,cAAA,8OAAC,gIAAA,CAAA,YAAS;oBAAC,WAAU;8BAAa,EAAE;;;;;;;;;;;0BAEtC,8OAAC,gIAAA,CAAA,cAAW;gBAAC,WAAU;;kCACrB,8OAAC;wBAAK,UAAU;wBAAc,WAAU;;0CACtC,8OAAC;;kDACC,8OAAC,iIAAA,CAAA,QAAK;wCAAC,SAAQ;wCAAoB,WAAU;kDAAiC,EAAE;;;;;;kDAChF,8OAAC,iIAAA,CAAA,QAAK;wCACJ,IAAG;wCACH,OAAO;wCACP,UAAU,CAAC,IAAM,UAAU,EAAE,MAAM,CAAC,KAAK;wCACzC,aAAa,EAAE;wCACf,WAAU;wCACV,UAAU;;;;;;;;;;;;4BAGb,iCACC,8OAAC;;kDACC,8OAAC,iIAAA,CAAA,QAAK;wCAAC,SAAQ;wCAAwB,WAAU;kDAAiC,EAAE;;;;;;kDACpF,8OAAC,oIAAA,CAAA,WAAQ;wCACP,IAAG;wCACH,OAAO;wCACP,QAAQ;wCACR,WAAU;;;;;;kDAEX,8OAAC;wCAAI,WAAU;kDACd,cAAA,8OAAC,kIAAA,CAAA,SAAM;4CACL,MAAK;4CACL,SAAQ;4CACR,MAAK;4CACL,SAAS;4CACT,UAAU,aAAa,CAAC;4CACxB,OAAO,EAAE;;8DAET,8OAAC,0NAAA,CAAA,iBAAc;oDAAC,WAAU;;;;;;gDACzB,EAAE;;;;;;;;;;;;;;;;;;0CAKX,8OAAC,kIAAA,CAAA,SAAM;gCAAC,MAAK;gCAAS,UAAU;gCAAW,WAAU;;oCAClD,0BAAY,8OAAC,iNAAA,CAAA,UAAO;wCAAC,WAAU;;;;;6DAAiC,8OAAC,0MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;oCACpF,EAAE;;;;;;;;;;;;;oBAIN,kBAAkB,MAAM,GAAG,mBACxB,8OAAC,qIAAA,CAAA,YAAS;wBAAC,MAAK;wBAAS,WAAW;wBAAC,WAAU;kCAC/C,cAAA,8OAAC,qIAAA,CAAA,gBAAa;4BAAC,OAAM;;8CACjB,8OAAC,qIAAA,CAAA,mBAAgB;oCAAC,WAAU;8CAC5B,cAAA,8OAAC;wCAAI,WAAU;;0DACX,8OAAC,wMAAA,CAAA,UAAO;gDAAC,WAAU;;;;;;4CAClB,EAAE;;;;;;;;;;;;8CAGP,8OAAC,qIAAA,CAAA,mBAAgB;oCAAC,WAAU;8CAC5B,cAAA,8OAAC;wCAAI,WAAU;kDACV,kBAAkB,GAAG,CAAC,CAAA,qBACvB,8OAAC,gIAAA,CAAA,OAAI;gDAAe,WAAU;;kEAC1B,8OAAC,gIAAA,CAAA,aAAU;wDAAC,WAAU;;0EACtB,8OAAC,iIAAA,CAAA,QAAK;gEAAC,WAAU;0EAAiC,EAAE;;;;;;0EACpD,8OAAC;gEAAE,WAAU;0EAAkC,KAAK,MAAM;;;;;;;;;;;;kEAE1D,8OAAC,gIAAA,CAAA,cAAW;wDAAC,WAAU;;0EACvB,8OAAC,iIAAA,CAAA,QAAK;gEAAC,WAAU;0EAAiC,EAAE;;;;;;0EACpD,8OAAC,oIAAA,CAAA,WAAQ;gEACL,OAAO,KAAK,aAAa;gEACzB,QAAQ;gEACR,WAAU;;;;;;;;;;;;kEAGd,8OAAC,gIAAA,CAAA,aAAU;wDAAC,WAAU;;0EACtB,8OAAC,kIAAA,CAAA,SAAM;gEACH,SAAQ;gEACR,MAAK;gEACL,SAAS;oEAAQ,aAAa,KAAK,aAAa;oEAAG,MAAM;wEAAE,UAAU;wEAAqB,gBAAgB;oEAA2B;gEAAG;gEACxI,OAAO,EAAE;0EAET,cAAA,8OAAC,0NAAA,CAAA,iBAAc;oEAAC,WAAU;;;;;;;;;;;0EAE9B,8OAAC,kIAAA,CAAA,SAAM;gEAAC,SAAQ;gEAAU,MAAK;gEAAK,SAAS,IAAM,gBAAgB,KAAK,MAAM;gEAAG,OAAO,EAAE;0EACtF,cAAA,8OAAC,uNAAA,CAAA,gBAAa;oEAAC,WAAU;;;;;;;;;;;0EAE7B,8OAAC,kIAAA,CAAA,SAAM;gEAAC,SAAQ;gEAAU,MAAK;gEAAK,SAAS,IAAM,iBAAiB,KAAK,aAAa;gEAAG,OAAO,EAAE;0EAC9F,cAAA,8OAAC,kMAAA,CAAA,OAAI;oEAAC,WAAU;;;;;;;;;;;;;;;;;;+CA1Bb,KAAK,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAuCtC", "debugId": null}}, {"offset": {"line": 7397, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/src/components/ai/ai-rewriter.tsx"], "sourcesContent": ["\n\"use client\";\n\nimport { useState, type FormEvent } from 'react';\nimport { But<PERSON> } from \"@/components/ui/button\";\nimport { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from \"@/components/ui/card\";\nimport { Textarea } from \"@/components/ui/textarea\";\nimport { Label } from \"@/components/ui/label\";\nimport { Loader2, CheckSquare, Wand2 } from \"lucide-react\";\n// Removed direct import of server action\ntype RephraseTextInput = {\n  text: string;\n  writingMode: string;\n  language: string;\n};\nimport { useToast } from '@/hooks/use-toast';\nimport { useI18n } from '@/contexts/i18n-context';\n\ninterface AiRewriterProps {\n  currentText: string;\n  writingMode: string;\n  onApplyRewrite: (text: string) => void;\n  direction: 'ltr' | 'rtl';\n}\n\nexport function AiRewriter({ currentText, writingMode, onApplyRewrite, direction }: AiRewriterProps) {\n  const [rewrittenOutput, setRewrittenOutput] = useState<string | null>(null);\n  const [isLoading, setIsLoading] = useState(false);\n  const { toast } = useToast();\n  const { t } = useI18n();\n\n  const handleSubmit = async (event: FormEvent<HTMLFormElement>) => {\n    event.preventDefault();\n    if (!currentText.trim()) {\n      toast({ titleKey: \"toastInputRequiredTitle\", descriptionKey: \"toastEditorEmptyError\", variant: \"destructive\" });\n      return;\n    }\n    setIsLoading(true);\n    setRewrittenOutput(null);\n\n    try {\n      const response = await fetch('/api/ai/rephrase-text', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({\n          selectedText: currentText,\n          contextText: currentText,\n          tone: writingMode,\n          style: writingMode,\n        }),\n      });\n\n      if (!response.ok) {\n        throw new Error(`HTTP error! status: ${response.status}`);\n      }\n\n      const result = await response.json();\n      if (result && result.rephrasedText) {\n        setRewrittenOutput(result.rephrasedText);\n        toast({ titleKey: \"toastSuccessTitle\", descriptionKey: \"toastRewriteSuccess\" });\n      } else {\n        throw new Error(\"AI did not return a valid rewrite.\");\n      }\n    } catch (error) {\n      console.error(\"Error rewriting text:\", error);\n      toast({ titleKey: \"toastErrorTitle\", descriptionKey: \"toastRewriteError\", variant: \"destructive\" });\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const handleApplyToEditor = () => {\n    if (rewrittenOutput) {\n      onApplyRewrite(rewrittenOutput);\n      toast({ titleKey: \"toastSuccessTitle\", descriptionKey: \"toastTextInsertedSuccess\" }); \n    }\n  };\n\n  return (\n    <Card className=\"border-none shadow-none\">\n      <CardHeader className=\"p-0 pb-4\">\n        <CardTitle className=\"text-base\">{t('aiRewriteDescription')}</CardTitle>\n      </CardHeader>\n      <form onSubmit={handleSubmit}>\n        <CardContent className=\"space-y-4 p-0\">\n          {rewrittenOutput && (\n            <div className=\"relative\">\n              <Label htmlFor=\"rewritten-text-output\" className=\"text-xs text-muted-foreground\">{t('rewrittenTextLabel')}</Label>\n              <Textarea\n                id=\"rewritten-text-output\"\n                value={rewrittenOutput}\n                readOnly\n                dir={direction}\n                className=\"mt-1 min-h-[120px] bg-muted font-code pr-24\"\n              />\n              <div className=\"absolute bottom-2 right-2\">\n                <Button\n                  type=\"button\"\n                  variant=\"outline\"\n                  size=\"sm\"\n                  onClick={handleApplyToEditor}\n                  disabled={isLoading || !rewrittenOutput}\n                  title={t('applyToEditorButton')}\n                >\n                  <CheckSquare className=\"mr-2 h-3.5 w-3.5\" />\n                  {t('applyToEditorButton')}\n                </Button>\n              </div>\n            </div>\n          )}\n          {!isLoading && !rewrittenOutput && currentText.trim().length === 0 && (\n            <div className=\"text-center py-8\">\n              <p className=\"text-sm text-muted-foreground\">{t('writeSomeTextToRewritePlaceholder')}</p>\n            </div>\n          )}\n           {!isLoading && !rewrittenOutput && currentText.trim().length > 0 && (\n             <div className=\"text-center py-8\">\n              <p className=\"text-sm text-muted-foreground\">{t('Click the button to rewrite the editor content.')}</p>\n            </div>\n          )}\n        </CardContent>\n        <CardFooter className=\"p-0 pt-4\">\n          <Button type=\"submit\" disabled={isLoading || currentText.trim().length === 0} className=\"w-full\">\n            {isLoading ? <Loader2 className=\"mr-2 h-4 w-4 animate-spin\" /> : <Wand2 className=\"mr-2 h-4 w-4\" />}\n            {t('rewriteEditorContentButton')}\n          </Button>\n        </CardFooter>\n      </form>\n    </Card>\n  );\n}\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAOA;AACA;AAfA;;;;;;;;;;AAwBO,SAAS,WAAW,EAAE,WAAW,EAAE,WAAW,EAAE,cAAc,EAAE,SAAS,EAAmB;IACjG,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IACtE,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,4HAAA,CAAA,WAAQ,AAAD;IACzB,MAAM,EAAE,CAAC,EAAE,GAAG,CAAA,GAAA,mIAAA,CAAA,UAAO,AAAD;IAEpB,MAAM,eAAe,OAAO;QAC1B,MAAM,cAAc;QACpB,IAAI,CAAC,YAAY,IAAI,IAAI;YACvB,MAAM;gBAAE,UAAU;gBAA2B,gBAAgB;gBAAyB,SAAS;YAAc;YAC7G;QACF;QACA,aAAa;QACb,mBAAmB;QAEnB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,yBAAyB;gBACpD,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBACnB,cAAc;oBACd,aAAa;oBACb,MAAM;oBACN,OAAO;gBACT;YACF;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;YAC1D;YAEA,MAAM,SAAS,MAAM,SAAS,IAAI;YAClC,IAAI,UAAU,OAAO,aAAa,EAAE;gBAClC,mBAAmB,OAAO,aAAa;gBACvC,MAAM;oBAAE,UAAU;oBAAqB,gBAAgB;gBAAsB;YAC/E,OAAO;gBACL,MAAM,IAAI,MAAM;YAClB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yBAAyB;YACvC,MAAM;gBAAE,UAAU;gBAAmB,gBAAgB;gBAAqB,SAAS;YAAc;QACnG,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,sBAAsB;QAC1B,IAAI,iBAAiB;YACnB,eAAe;YACf,MAAM;gBAAE,UAAU;gBAAqB,gBAAgB;YAA2B;QACpF;IACF;IAEA,qBACE,8OAAC,gIAAA,CAAA,OAAI;QAAC,WAAU;;0BACd,8OAAC,gIAAA,CAAA,aAAU;gBAAC,WAAU;0BACpB,cAAA,8OAAC,gIAAA,CAAA,YAAS;oBAAC,WAAU;8BAAa,EAAE;;;;;;;;;;;0BAEtC,8OAAC;gBAAK,UAAU;;kCACd,8OAAC,gIAAA,CAAA,cAAW;wBAAC,WAAU;;4BACpB,iCACC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,iIAAA,CAAA,QAAK;wCAAC,SAAQ;wCAAwB,WAAU;kDAAiC,EAAE;;;;;;kDACpF,8OAAC,oIAAA,CAAA,WAAQ;wCACP,IAAG;wCACH,OAAO;wCACP,QAAQ;wCACR,KAAK;wCACL,WAAU;;;;;;kDAEZ,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,kIAAA,CAAA,SAAM;4CACL,MAAK;4CACL,SAAQ;4CACR,MAAK;4CACL,SAAS;4CACT,UAAU,aAAa,CAAC;4CACxB,OAAO,EAAE;;8DAET,8OAAC,2NAAA,CAAA,cAAW;oDAAC,WAAU;;;;;;gDACtB,EAAE;;;;;;;;;;;;;;;;;;4BAKV,CAAC,aAAa,CAAC,mBAAmB,YAAY,IAAI,GAAG,MAAM,KAAK,mBAC/D,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAE,WAAU;8CAAiC,EAAE;;;;;;;;;;;4BAGlD,CAAC,aAAa,CAAC,mBAAmB,YAAY,IAAI,GAAG,MAAM,GAAG,mBAC7D,8OAAC;gCAAI,WAAU;0CACd,cAAA,8OAAC;oCAAE,WAAU;8CAAiC,EAAE;;;;;;;;;;;;;;;;;kCAItD,8OAAC,gIAAA,CAAA,aAAU;wBAAC,WAAU;kCACpB,cAAA,8OAAC,kIAAA,CAAA,SAAM;4BAAC,MAAK;4BAAS,UAAU,aAAa,YAAY,IAAI,GAAG,MAAM,KAAK;4BAAG,WAAU;;gCACrF,0BAAY,8OAAC,iNAAA,CAAA,UAAO;oCAAC,WAAU;;;;;yDAAiC,8OAAC,+MAAA,CAAA,QAAK;oCAAC,WAAU;;;;;;gCACjF,EAAE;;;;;;;;;;;;;;;;;;;;;;;;AAMf", "debugId": null}}, {"offset": {"line": 7653, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/src/components/ui/progress.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as ProgressPrimitive from \"@radix-ui/react-progress\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Progress = React.forwardRef<\n  React.ElementRef<typeof ProgressPrimitive.Root>,\n  React.ComponentPropsWithoutRef<typeof ProgressPrimitive.Root>\n>(({ className, value, ...props }, ref) => (\n  <ProgressPrimitive.Root\n    ref={ref}\n    className={cn(\n      \"relative h-4 w-full overflow-hidden rounded-full bg-secondary\",\n      className\n    )}\n    {...props}\n  >\n    <ProgressPrimitive.Indicator\n      className=\"h-full w-full flex-1 bg-primary transition-all\"\n      style={{ transform: `translateX(-${100 - (value || 0)}%)` }}\n    />\n  </ProgressPrimitive.Root>\n))\nProgress.displayName = ProgressPrimitive.Root.displayName\n\nexport { Progress }\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAEA;AALA;;;;;AAOA,MAAM,yBAAW,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG9B,CAAC,EAAE,SAAS,EAAE,KAAK,EAAE,GAAG,OAAO,EAAE,oBACjC,8OAAC,oKAAA,CAAA,OAAsB;QACrB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,iEACA;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC,oKAAA,CAAA,YAA2B;YAC1B,WAAU;YACV,OAAO;gBAAE,WAAW,CAAC,YAAY,EAAE,MAAM,CAAC,SAAS,CAAC,EAAE,EAAE,CAAC;YAAC;;;;;;;;;;;AAIhE,SAAS,WAAW,GAAG,oKAAA,CAAA,OAAsB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 7692, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/src/components/ai/plagiarism-detector.tsx"], "sourcesContent": ["\n\"use client\";\n\nimport { useState, type FormEvent, useEffect } from 'react';\nimport { But<PERSON> } from \"@/components/ui/button\";\nimport { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>er, <PERSON><PERSON>eader, CardTitle } from \"@/components/ui/card\";\nimport { <PERSON><PERSON><PERSON><PERSON>, Loader2, Al<PERSON><PERSON>riangle } from \"lucide-react\";\nimport { useToast } from '@/hooks/use-toast';\nimport { useI18n } from '@/contexts/i18n-context';\nimport { Progress } from '@/components/ui/progress';\nimport { type PlagiarismDetectionOutput } from '@/ai/flows/plagiarism-detection-flow';\n\ninterface PlagiarismDetectorProps {\n  currentText: string;\n  onResult: (result: PlagiarismDetectionOutput | null) => void;\n}\n\nexport function PlagiarismDetector({ currentText, onResult }: PlagiarismDetectorProps) {\n  const [analysisResult, setAnalysisResult] = useState<PlagiarismDetectionOutput | null>(null);\n  const [isLoading, setIsLoading] = useState(false);\n  const { toast } = useToast();\n  const { t } = useI18n();\n\n  useEffect(() => {\n    onResult(analysisResult);\n  // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [analysisResult]);\n\n  const handleSubmit = async (event: FormEvent<HTMLFormElement>) => {\n    event.preventDefault();\n    if (!currentText.trim()) {\n      toast({ titleKey: \"toastInputRequiredTitle\", descriptionKey: \"toastEditorEmptyError\", variant: \"destructive\" });\n      return;\n    }\n    setIsLoading(true);\n    setAnalysisResult(null);\n\n    try {\n      const response = await fetch('/api/ai/detect-plagiarism', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({ text: currentText }),\n      });\n\n      if (!response.ok) {\n        throw new Error(`HTTP error! status: ${response.status}`);\n      }\n\n      const result = await response.json();\n      if (result) {\n        setAnalysisResult(result);\n        toast({ titleKey: \"toastSuccessTitle\", descriptionKey: \"toastPlagiarismDetectionSuccess\" });\n      } else {\n        throw new Error(\"Plagiarism detection returned no result.\");\n      }\n    } catch (error) {\n      console.error(\"Error detecting plagiarism:\", error);\n      toast({ titleKey: \"toastErrorTitle\", descriptionKey: \"toastPlagiarismDetectionError\", variant: \"destructive\" });\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  return (\n    <Card className=\"border-none shadow-none\">\n       <CardHeader className=\"p-0 pb-4\">\n        <CardTitle className=\"text-base\">{t('plagiarismDetectionDescription')}</CardTitle>\n      </CardHeader>\n      <form onSubmit={handleSubmit}>\n        <CardContent className=\"space-y-4 p-0\">\n          {analysisResult && (\n            <div className=\"space-y-3 p-3 bg-muted rounded-md\">\n              <div>\n                <h4 className=\"font-semibold text-sm\">{t('originalityScoreLabel')}:</h4>\n                <div className=\"flex items-center gap-2 mt-1\">\n                    <Progress value={analysisResult.originalityScore} className=\"w-[calc(100%-4rem)] h-2.5\" />\n                    <span className=\"text-sm text-foreground font-medium\">{analysisResult.originalityScore} / 100</span>\n                </div>\n              </div>\n              <div>\n                <h4 className=\"font-semibold text-sm mt-2\">{t('plagiarismReportLabel')}:</h4>\n                <p className=\"text-sm text-muted-foreground\">{analysisResult.analysisReport}</p>\n              </div>\n\n              {analysisResult.detectedSources && analysisResult.detectedSources.length > 0 && (\n                <div className=\"mt-4 space-y-3\">\n                  <h4 className=\"font-semibold text-base flex items-center text-destructive\">\n                    <AlertTriangle className=\"mr-2 h-4 w-4\" />\n                    {t('potentialSourcesFoundLabel')}\n                  </h4>\n                  <div className=\"space-y-2\">\n                    {analysisResult.detectedSources.map((source, index) => (\n                      <div key={index} className=\"p-3 border-l-4 border-destructive bg-destructive/5 rounded-r-md\">\n                        <blockquote className=\"border-l-0 p-0\">\n                          <p className=\"text-sm font-medium text-destructive leading-relaxed\">\n                            \"{source.plagiarizedSegment}\"\n                          </p>\n                        </blockquote>\n                        <div className=\"mt-2 text-xs text-muted-foreground\">\n                          <p>\n                            <span className=\"font-semibold\">{t('similarityScoreLabel')}:</span> {source.similarityScore}%\n                          </p>\n                          <p>\n                            <span className=\"font-semibold\">{t('originalSourceLabel')}:</span>{' '}\n                            <a href={source.originalSource} target=\"_blank\" rel=\"noopener noreferrer\" className=\"underline hover:text-destructive transition-colors break-all\">\n                              {source.originalSource}\n                            </a>\n                          </p>\n                        </div>\n                      </div>\n                    ))}\n                  </div>\n                </div>\n              )}\n            </div>\n          )}\n          {!isLoading && !analysisResult && currentText.trim().length === 0 && (\n            <p className=\"text-sm text-muted-foreground text-center py-8\">{t('writeSomeTextToDetectPlagiarismPlaceholder')}</p>\n          )}\n        </CardContent>\n        <CardFooter className=\"p-0 pt-4\">\n          <Button type=\"submit\" disabled={isLoading || currentText.trim().length === 0} className=\"w-full\">\n            {isLoading ? <Loader2 className=\"mr-2 h-4 w-4 animate-spin\" /> : <ShieldCheck className=\"mr-2 h-4 w-4\" />}\n            {t('detectPlagiarismButton')}\n          </Button>\n        </CardFooter>\n      </form>\n    </Card>\n  );\n}\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AACA;AAAA;AAAA;AACA;AACA;AACA;AARA;;;;;;;;;AAgBO,SAAS,mBAAmB,EAAE,WAAW,EAAE,QAAQ,EAA2B;IACnF,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAoC;IACvF,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,4HAAA,CAAA,WAAQ,AAAD;IACzB,MAAM,EAAE,CAAC,EAAE,GAAG,CAAA,GAAA,mIAAA,CAAA,UAAO,AAAD;IAEpB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,SAAS;IACX,uDAAuD;IACvD,GAAG;QAAC;KAAe;IAEnB,MAAM,eAAe,OAAO;QAC1B,MAAM,cAAc;QACpB,IAAI,CAAC,YAAY,IAAI,IAAI;YACvB,MAAM;gBAAE,UAAU;gBAA2B,gBAAgB;gBAAyB,SAAS;YAAc;YAC7G;QACF;QACA,aAAa;QACb,kBAAkB;QAElB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,6BAA6B;gBACxD,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBAAE,MAAM;gBAAY;YAC3C;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;YAC1D;YAEA,MAAM,SAAS,MAAM,SAAS,IAAI;YAClC,IAAI,QAAQ;gBACV,kBAAkB;gBAClB,MAAM;oBAAE,UAAU;oBAAqB,gBAAgB;gBAAkC;YAC3F,OAAO;gBACL,MAAM,IAAI,MAAM;YAClB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,+BAA+B;YAC7C,MAAM;gBAAE,UAAU;gBAAmB,gBAAgB;gBAAiC,SAAS;YAAc;QAC/G,SAAU;YACR,aAAa;QACf;IACF;IAEA,qBACE,8OAAC,gIAAA,CAAA,OAAI;QAAC,WAAU;;0BACb,8OAAC,gIAAA,CAAA,aAAU;gBAAC,WAAU;0BACrB,cAAA,8OAAC,gIAAA,CAAA,YAAS;oBAAC,WAAU;8BAAa,EAAE;;;;;;;;;;;0BAEtC,8OAAC;gBAAK,UAAU;;kCACd,8OAAC,gIAAA,CAAA,cAAW;wBAAC,WAAU;;4BACpB,gCACC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;;0DACC,8OAAC;gDAAG,WAAU;;oDAAyB,EAAE;oDAAyB;;;;;;;0DAClE,8OAAC;gDAAI,WAAU;;kEACX,8OAAC,oIAAA,CAAA,WAAQ;wDAAC,OAAO,eAAe,gBAAgB;wDAAE,WAAU;;;;;;kEAC5D,8OAAC;wDAAK,WAAU;;4DAAuC,eAAe,gBAAgB;4DAAC;;;;;;;;;;;;;;;;;;;kDAG7F,8OAAC;;0DACC,8OAAC;gDAAG,WAAU;;oDAA8B,EAAE;oDAAyB;;;;;;;0DACvE,8OAAC;gDAAE,WAAU;0DAAiC,eAAe,cAAc;;;;;;;;;;;;oCAG5E,eAAe,eAAe,IAAI,eAAe,eAAe,CAAC,MAAM,GAAG,mBACzE,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;;kEACZ,8OAAC,wNAAA,CAAA,gBAAa;wDAAC,WAAU;;;;;;oDACxB,EAAE;;;;;;;0DAEL,8OAAC;gDAAI,WAAU;0DACZ,eAAe,eAAe,CAAC,GAAG,CAAC,CAAC,QAAQ,sBAC3C,8OAAC;wDAAgB,WAAU;;0EACzB,8OAAC;gEAAW,WAAU;0EACpB,cAAA,8OAAC;oEAAE,WAAU;;wEAAuD;wEAChE,OAAO,kBAAkB;wEAAC;;;;;;;;;;;;0EAGhC,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;;0FACC,8OAAC;gFAAK,WAAU;;oFAAiB,EAAE;oFAAwB;;;;;;;4EAAQ;4EAAE,OAAO,eAAe;4EAAC;;;;;;;kFAE9F,8OAAC;;0FACC,8OAAC;gFAAK,WAAU;;oFAAiB,EAAE;oFAAuB;;;;;;;4EAAS;0FACnE,8OAAC;gFAAE,MAAM,OAAO,cAAc;gFAAE,QAAO;gFAAS,KAAI;gFAAsB,WAAU;0FACjF,OAAO,cAAc;;;;;;;;;;;;;;;;;;;uDAbpB;;;;;;;;;;;;;;;;;;;;;;4BAwBrB,CAAC,aAAa,CAAC,kBAAkB,YAAY,IAAI,GAAG,MAAM,KAAK,mBAC9D,8OAAC;gCAAE,WAAU;0CAAkD,EAAE;;;;;;;;;;;;kCAGrE,8OAAC,gIAAA,CAAA,aAAU;wBAAC,WAAU;kCACpB,cAAA,8OAAC,kIAAA,CAAA,SAAM;4BAAC,MAAK;4BAAS,UAAU,aAAa,YAAY,IAAI,GAAG,MAAM,KAAK;4BAAG,WAAU;;gCACrF,0BAAY,8OAAC,iNAAA,CAAA,UAAO;oCAAC,WAAU;;;;;yDAAiC,8OAAC,oNAAA,CAAA,cAAW;oCAAC,WAAU;;;;;;gCACvF,EAAE;;;;;;;;;;;;;;;;;;;;;;;;AAMf", "debugId": null}}, {"offset": {"line": 8066, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/src/components/ai/ai-writing-detector.tsx"], "sourcesContent": ["\n\"use client\";\n\nimport { useState, type FormEvent } from 'react';\nimport { But<PERSON> } from \"@/components/ui/button\";\nimport { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTit<PERSON> } from \"@/components/ui/card\";\nimport { BrainCircuit, Loader2 } from \"lucide-react\";\n// Removed direct import of server action\ntype AiWritingDetectionOutput = {\n  isAiGenerated: boolean;\n  confidence: number;\n  reasoning: string;\n} | null;\nimport { useToast } from '@/hooks/use-toast';\nimport { useI18n } from '@/contexts/i18n-context';\nimport { Progress } from '@/components/ui/progress';\n\ninterface AiWritingDetectorProps {\n  currentText: string;\n}\n\nexport function AiWritingDetector({ currentText }: AiWritingDetectorProps) {\n  const [analysisResult, setAnalysisResult] = useState<AiWritingDetectionOutput | null>(null);\n  const [isLoading, setIsLoading] = useState(false);\n  const { toast } = useToast();\n  const { t } = useI18n();\n\n  const handleSubmit = async (event: FormEvent<HTMLFormElement>) => {\n    event.preventDefault();\n    if (!currentText.trim()) {\n      toast({ titleKey: \"toastInputRequiredTitle\", descriptionKey: \"toastEditorEmptyError\", variant: \"destructive\" });\n      return;\n    }\n    setIsLoading(true);\n    setAnalysisResult(null);\n\n    try {\n      const response = await fetch('/api/ai/detect-ai-writing', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({ text: currentText }),\n      });\n\n      if (!response.ok) {\n        throw new Error(`HTTP error! status: ${response.status}`);\n      }\n\n      const result = await response.json();\n      if (result) {\n        setAnalysisResult(result);\n        toast({ titleKey: \"toastSuccessTitle\", descriptionKey: \"toastAiWritingDetectionSuccess\" });\n      } else {\n        throw new Error(\"AI Writing Detection returned no result.\");\n      }\n    } catch (error) {\n      console.error(\"Error detecting AI writing:\", error);\n      toast({ titleKey: \"toastErrorTitle\", descriptionKey: \"toastAiWritingDetectionError\", variant: \"destructive\" });\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  return (\n    <Card className=\"border-none shadow-none\">\n      <CardHeader className=\"p-0 pb-4\">\n        <CardTitle className=\"text-base\">{t('aiWritingDetectionDescription')}</CardTitle>\n      </CardHeader>\n      <form onSubmit={handleSubmit}>\n        <CardContent className=\"space-y-4 p-0\">\n          {analysisResult && (\n            <div className=\"space-y-3 p-3 bg-muted rounded-md\">\n              <div>\n                <h4 className=\"font-semibold text-sm\">{t('probabilityAIWrittenLabel')}:</h4>\n                <div className=\"flex items-center gap-2 mt-1\">\n                    <Progress value={analysisResult.probabilityAIWritten} className=\"w-[calc(100%-4rem)] h-2.5\" />\n                    <span className=\"text-sm text-foreground font-medium\">{analysisResult.probabilityAIWritten} / 100</span>\n                </div>\n              </div>\n              <div>\n                <h4 className=\"font-semibold text-sm mt-2\">{t('aiWritingDetectionSummaryLabel')}:</h4>\n                <p className=\"text-sm text-muted-foreground\">{analysisResult.summary}</p>\n              </div>\n            </div>\n          )}\n          {!isLoading && !analysisResult && currentText.trim().length === 0 && (\n            <p className=\"text-sm text-muted-foreground text-center py-8\">{t('writeSomeTextToDetectAiWritingPlaceholder')}</p>\n          )}\n        </CardContent>\n        <CardFooter className=\"p-0 pt-4\">\n          <Button type=\"submit\" disabled={isLoading || currentText.trim().length === 0} className=\"w-full\">\n            {isLoading ? <Loader2 className=\"mr-2 h-4 w-4 animate-spin\" /> : <BrainCircuit className=\"mr-2 h-4 w-4\" />}\n            {t('detectAiWritingButton')}\n          </Button>\n        </CardFooter>\n      </form>\n    </Card>\n  );\n}\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AACA;AAAA;AAOA;AACA;AACA;AAdA;;;;;;;;;AAoBO,SAAS,kBAAkB,EAAE,WAAW,EAA0B;IACvE,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAmC;IACtF,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,4HAAA,CAAA,WAAQ,AAAD;IACzB,MAAM,EAAE,CAAC,EAAE,GAAG,CAAA,GAAA,mIAAA,CAAA,UAAO,AAAD;IAEpB,MAAM,eAAe,OAAO;QAC1B,MAAM,cAAc;QACpB,IAAI,CAAC,YAAY,IAAI,IAAI;YACvB,MAAM;gBAAE,UAAU;gBAA2B,gBAAgB;gBAAyB,SAAS;YAAc;YAC7G;QACF;QACA,aAAa;QACb,kBAAkB;QAElB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,6BAA6B;gBACxD,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBAAE,MAAM;gBAAY;YAC3C;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;YAC1D;YAEA,MAAM,SAAS,MAAM,SAAS,IAAI;YAClC,IAAI,QAAQ;gBACV,kBAAkB;gBAClB,MAAM;oBAAE,UAAU;oBAAqB,gBAAgB;gBAAiC;YAC1F,OAAO;gBACL,MAAM,IAAI,MAAM;YAClB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,+BAA+B;YAC7C,MAAM;gBAAE,UAAU;gBAAmB,gBAAgB;gBAAgC,SAAS;YAAc;QAC9G,SAAU;YACR,aAAa;QACf;IACF;IAEA,qBACE,8OAAC,gIAAA,CAAA,OAAI;QAAC,WAAU;;0BACd,8OAAC,gIAAA,CAAA,aAAU;gBAAC,WAAU;0BACpB,cAAA,8OAAC,gIAAA,CAAA,YAAS;oBAAC,WAAU;8BAAa,EAAE;;;;;;;;;;;0BAEtC,8OAAC;gBAAK,UAAU;;kCACd,8OAAC,gIAAA,CAAA,cAAW;wBAAC,WAAU;;4BACpB,gCACC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;;0DACC,8OAAC;gDAAG,WAAU;;oDAAyB,EAAE;oDAA6B;;;;;;;0DACtE,8OAAC;gDAAI,WAAU;;kEACX,8OAAC,oIAAA,CAAA,WAAQ;wDAAC,OAAO,eAAe,oBAAoB;wDAAE,WAAU;;;;;;kEAChE,8OAAC;wDAAK,WAAU;;4DAAuC,eAAe,oBAAoB;4DAAC;;;;;;;;;;;;;;;;;;;kDAGjG,8OAAC;;0DACC,8OAAC;gDAAG,WAAU;;oDAA8B,EAAE;oDAAkC;;;;;;;0DAChF,8OAAC;gDAAE,WAAU;0DAAiC,eAAe,OAAO;;;;;;;;;;;;;;;;;;4BAIzE,CAAC,aAAa,CAAC,kBAAkB,YAAY,IAAI,GAAG,MAAM,KAAK,mBAC9D,8OAAC;gCAAE,WAAU;0CAAkD,EAAE;;;;;;;;;;;;kCAGrE,8OAAC,gIAAA,CAAA,aAAU;wBAAC,WAAU;kCACpB,cAAA,8OAAC,kIAAA,CAAA,SAAM;4BAAC,MAAK;4BAAS,UAAU,aAAa,YAAY,IAAI,GAAG,MAAM,KAAK;4BAAG,WAAU;;gCACrF,0BAAY,8OAAC,iNAAA,CAAA,UAAO;oCAAC,WAAU;;;;;yDAAiC,8OAAC,sNAAA,CAAA,eAAY;oCAAC,WAAU;;;;;;gCACxF,EAAE;;;;;;;;;;;;;;;;;;;;;;;;AAMf", "debugId": null}}, {"offset": {"line": 8310, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/src/components/ai/humanize-ai-text.tsx"], "sourcesContent": ["\n\"use client\";\n\nimport { useState, type FormEvent } from 'react';\nimport { But<PERSON> } from \"@/components/ui/button\";\nimport { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>it<PERSON> } from \"@/components/ui/card\";\nimport { Textarea } from \"@/components/ui/textarea\";\nimport { Label } from \"@/components/ui/label\";\nimport { User<PERSON>heck, Loader2, ClipboardPaste } from \"lucide-react\";\n// Removed direct import of server action\ntype HumanizeTextOutput = {\n  humanizedText: string;\n};\nimport { useToast } from '@/hooks/use-toast';\nimport { useI18n } from '@/contexts/i18n-context';\n\ninterface HumanizeAiTextProps {\n  currentText: string;\n  onInsertText: (text: string) => void;\n}\n\nexport function HumanizeAiText({ currentText, onInsertText }: HumanizeAiTextProps) {\n  const [humanizedOutput, setHumanizedOutput] = useState<HumanizeTextOutput | null>(null);\n  const [isLoading, setIsLoading] = useState(false);\n  const { toast } = useToast();\n  const { t } = useI18n();\n\n  const handleSubmit = async (event: FormEvent<HTMLFormElement>) => {\n    event.preventDefault();\n    if (!currentText.trim()) {\n      toast({ titleKey: \"toastInputRequiredTitle\", descriptionKey: \"toastEditorEmptyError\", variant: \"destructive\" });\n      return;\n    }\n    setIsLoading(true);\n    setHumanizedOutput(null);\n\n    try {\n      const response = await fetch('/api/ai/humanize-text', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({ text: currentText }),\n      });\n\n      if (!response.ok) {\n        throw new Error(`HTTP error! status: ${response.status}`);\n      }\n\n      const result = await response.json();\n      if (result) {\n        setHumanizedOutput(result);\n        toast({ titleKey: \"toastSuccessTitle\", descriptionKey: \"toastHumanizeTextSuccess\" });\n      } else {\n        throw new Error(\"Humanize text returned no result.\");\n      }\n    } catch (error) {\n      console.error(\"Error humanizing text:\", error);\n      toast({ titleKey: \"toastErrorTitle\", descriptionKey: \"toastHumanizeTextError\", variant: \"destructive\" });\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const handleInsertIntoEditor = () => {\n    if (humanizedOutput?.humanizedText) {\n      onInsertText(humanizedOutput.humanizedText);\n      toast({ titleKey: \"toastSuccessTitle\", descriptionKey: \"toastTextInsertedSuccess\" });\n    }\n  };\n\n  return (\n    <Card className=\"border-none shadow-none\">\n       <CardHeader className=\"p-0 pb-4\">\n        <CardTitle className=\"text-base\">{t('humanizeAiTextDescription')}</CardTitle>\n      </CardHeader>\n      <form onSubmit={handleSubmit}>\n        <CardContent className=\"space-y-4 p-0\">\n          {humanizedOutput && (\n            <div>\n              <Label htmlFor=\"humanized-text-output\" className=\"text-xs text-muted-foreground\">{t('humanizedTextLabel')}</Label>\n              <Textarea\n                id=\"humanized-text-output\"\n                value={humanizedOutput.humanizedText}\n                readOnly\n                className=\"mt-1 min-h-[120px] bg-muted font-code\"\n              />\n              <div className=\"mt-2 flex justify-end\">\n                <Button\n                  type=\"button\"\n                  variant=\"outline\"\n                  size=\"sm\"\n                  onClick={handleInsertIntoEditor}\n                  disabled={isLoading || !humanizedOutput?.humanizedText}\n                  title={t('insertIntoEditorButtonTooltip')}\n                >\n                  <ClipboardPaste className=\"mr-2 h-3.5 w-3.5\" />\n                  {t('insertIntoEditorButton')}\n                </Button>\n              </div>\n            </div>\n          )}\n          {!isLoading && !humanizedOutput && currentText.trim().length === 0 && (\n            <p className=\"text-sm text-muted-foreground text-center py-8\">{t('writeSomeTextToHumanizePlaceholder')}</p>\n          )}\n        </CardContent>\n        <CardFooter className=\"p-0 pt-4\">\n          <Button type=\"submit\" disabled={isLoading || currentText.trim().length === 0} className=\"w-full\">\n            {isLoading ? <Loader2 className=\"mr-2 h-4 w-4 animate-spin\" /> : <UserCheck className=\"mr-2 h-4 w-4\" />}\n            {t('humanizeTextButton')}\n          </Button>\n        </CardFooter>\n      </form>\n    </Card>\n  );\n}\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAKA;AACA;AAbA;;;;;;;;;;AAoBO,SAAS,eAAe,EAAE,WAAW,EAAE,YAAY,EAAuB;IAC/E,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA6B;IAClF,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,4HAAA,CAAA,WAAQ,AAAD;IACzB,MAAM,EAAE,CAAC,EAAE,GAAG,CAAA,GAAA,mIAAA,CAAA,UAAO,AAAD;IAEpB,MAAM,eAAe,OAAO;QAC1B,MAAM,cAAc;QACpB,IAAI,CAAC,YAAY,IAAI,IAAI;YACvB,MAAM;gBAAE,UAAU;gBAA2B,gBAAgB;gBAAyB,SAAS;YAAc;YAC7G;QACF;QACA,aAAa;QACb,mBAAmB;QAEnB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,yBAAyB;gBACpD,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBAAE,MAAM;gBAAY;YAC3C;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;YAC1D;YAEA,MAAM,SAAS,MAAM,SAAS,IAAI;YAClC,IAAI,QAAQ;gBACV,mBAAmB;gBACnB,MAAM;oBAAE,UAAU;oBAAqB,gBAAgB;gBAA2B;YACpF,OAAO;gBACL,MAAM,IAAI,MAAM;YAClB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;YACxC,MAAM;gBAAE,UAAU;gBAAmB,gBAAgB;gBAA0B,SAAS;YAAc;QACxG,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,yBAAyB;QAC7B,IAAI,iBAAiB,eAAe;YAClC,aAAa,gBAAgB,aAAa;YAC1C,MAAM;gBAAE,UAAU;gBAAqB,gBAAgB;YAA2B;QACpF;IACF;IAEA,qBACE,8OAAC,gIAAA,CAAA,OAAI;QAAC,WAAU;;0BACb,8OAAC,gIAAA,CAAA,aAAU;gBAAC,WAAU;0BACrB,cAAA,8OAAC,gIAAA,CAAA,YAAS;oBAAC,WAAU;8BAAa,EAAE;;;;;;;;;;;0BAEtC,8OAAC;gBAAK,UAAU;;kCACd,8OAAC,gIAAA,CAAA,cAAW;wBAAC,WAAU;;4BACpB,iCACC,8OAAC;;kDACC,8OAAC,iIAAA,CAAA,QAAK;wCAAC,SAAQ;wCAAwB,WAAU;kDAAiC,EAAE;;;;;;kDACpF,8OAAC,oIAAA,CAAA,WAAQ;wCACP,IAAG;wCACH,OAAO,gBAAgB,aAAa;wCACpC,QAAQ;wCACR,WAAU;;;;;;kDAEZ,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,kIAAA,CAAA,SAAM;4CACL,MAAK;4CACL,SAAQ;4CACR,MAAK;4CACL,SAAS;4CACT,UAAU,aAAa,CAAC,iBAAiB;4CACzC,OAAO,EAAE;;8DAET,8OAAC,0NAAA,CAAA,iBAAc;oDAAC,WAAU;;;;;;gDACzB,EAAE;;;;;;;;;;;;;;;;;;4BAKV,CAAC,aAAa,CAAC,mBAAmB,YAAY,IAAI,GAAG,MAAM,KAAK,mBAC/D,8OAAC;gCAAE,WAAU;0CAAkD,EAAE;;;;;;;;;;;;kCAGrE,8OAAC,gIAAA,CAAA,aAAU;wBAAC,WAAU;kCACpB,cAAA,8OAAC,kIAAA,CAAA,SAAM;4BAAC,MAAK;4BAAS,UAAU,aAAa,YAAY,IAAI,GAAG,MAAM,KAAK;4BAAG,WAAU;;gCACrF,0BAAY,8OAAC,iNAAA,CAAA,UAAO;oCAAC,WAAU;;;;;yDAAiC,8OAAC,gNAAA,CAAA,YAAS;oCAAC,WAAU;;;;;;gCACrF,EAAE;;;;;;;;;;;;;;;;;;;;;;;;AAMf", "debugId": null}}, {"offset": {"line": 8539, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/src/components/ui/tabs.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as TabsPrimitive from \"@radix-ui/react-tabs\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Tabs = TabsPrimitive.Root\n\nconst TabsList = React.forwardRef<\n  React.ElementRef<typeof TabsPrimitive.List>,\n  React.ComponentPropsWithoutRef<typeof TabsPrimitive.List>\n>(({ className, ...props }, ref) => (\n  <TabsPrimitive.List\n    ref={ref}\n    className={cn(\n      \"inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground\",\n      className\n    )}\n    {...props}\n  />\n))\nTabsList.displayName = TabsPrimitive.List.displayName\n\nconst TabsTrigger = React.forwardRef<\n  React.ElementRef<typeof TabsPrimitive.Trigger>,\n  React.ComponentPropsWithoutRef<typeof TabsPrimitive.Trigger>\n>(({ className, ...props }, ref) => (\n  <TabsPrimitive.Trigger\n    ref={ref}\n    className={cn(\n      \"inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm\",\n      className\n    )}\n    {...props}\n  />\n))\nTabsTrigger.displayName = TabsPrimitive.Trigger.displayName\n\nconst TabsContent = React.forwardRef<\n  React.ElementRef<typeof TabsPrimitive.Content>,\n  React.ComponentPropsWithoutRef<typeof TabsPrimitive.Content>\n>(({ className, ...props }, ref) => (\n  <TabsPrimitive.Content\n    ref={ref}\n    className={cn(\n      \"mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2\",\n      className\n    )}\n    {...props}\n  />\n))\nTabsContent.displayName = TabsPrimitive.Content.displayName\n\nexport { Tabs, TabsList, TabsTrigger, TabsContent }\n"], "names": [], "mappings": ";;;;;;;AAEA;AACA;AAEA;AALA;;;;;AAOA,MAAM,OAAO,gKAAA,CAAA,OAAkB;AAE/B,MAAM,yBAAW,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG9B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,gKAAA,CAAA,OAAkB;QACjB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8FACA;QAED,GAAG,KAAK;;;;;;AAGb,SAAS,WAAW,GAAG,gKAAA,CAAA,OAAkB,CAAC,WAAW;AAErD,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,gKAAA,CAAA,UAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,uYACA;QAED,GAAG,KAAK;;;;;;AAGb,YAAY,WAAW,GAAG,gKAAA,CAAA,UAAqB,CAAC,WAAW;AAE3D,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,gKAAA,CAAA,UAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,mIACA;QAED,GAAG,KAAK;;;;;;AAGb,YAAY,WAAW,GAAG,gKAAA,CAAA,UAAqB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 8592, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/src/app/page.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useState, useEffect, useCallback, useRef, useMemo } from 'react';\nimport { AppShell } from '@/components/layout/app-shell';\nimport {\n  EnhancedDocumentEditor,\n  DocumentToolbar,\n  DocumentStatusBar,\n  WritingSuggestionsPanel,\n  type EnhancedSuggestion,\n  type PlagiarismSource,\n  type SimilaritySource\n} from '@/components/editor';\nimport { AiToneAnalyzer } from '@/components/ai/ai-tone-analyzer';\nimport { AiTextGenerator } from '@/components/ai/ai-text-generator';\nimport { AiRewriter } from '@/components/ai/ai-rewriter';\nimport { PlagiarismDetector } from '@/components/ai/plagiarism-detector';\nimport { AiWritingDetector } from '@/components/ai/ai-writing-detector';\nimport { HumanizeAiText } from '@/components/ai/humanize-ai-text';\nimport { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';\nimport { useToast } from '@/hooks/use-toast';\nimport { useI18n } from '@/contexts/i18n-context';\nimport { useFeatureSettings } from '@/contexts/feature-settings-context';\nimport { type PlagiarismDetectionOutput } from '@/ai/flows/plagiarism-detection-flow';\n// Removed direct imports of server actions - now using API routes\ntype TextAnalysisInput = {\n  text: string;\n  language: string;\n};\n\ntype AnalysisSuggestion = {\n  id: string;\n  type: 'style' | 'spelling' | 'grammar' | 'rewrite';\n  message: string;\n  suggestion: string;\n  originalSegment: string;\n  severity: 'low' | 'medium' | 'high';\n  startIndex?: number;\n  endIndex?: number;\n  suggestions?: string[];\n};\n\ntype EnhancedTextAnalysisInput = {\n  text: string;\n  language: string;\n  focusAreas?: ('vocabulary' | 'rhythm' | 'flow' | 'engagement' | 'clarity')[];\n  avoidRepetition?: boolean;\n};\n\ntype EnhancedSuggestionType = {\n  type: string;\n  message: string;\n  severity: 'low' | 'medium' | 'high';\n  startIndex: number;\n  endIndex: number;\n  suggestions: string[];\n  confidence: number;\n  category: string;\n};\nimport { APP_WRITING_LANGUAGES } from '@/config/languages';\nimport { Edit3, Wand2, ShieldCheck, BrainCircuit, UserCheck, Gauge } from 'lucide-react';\n// Removed type import - now defined locally\n\n// Debounce function\nfunction debounce<F extends (...args: any[]) => any>(func: F, waitFor: number): (...args: Parameters<F>) => void {\n  let timeout: ReturnType<typeof setTimeout>;\n\n  const debounced = (...args: Parameters<F>) => {\n    clearTimeout(timeout);\n    timeout = setTimeout(() => func(...args), waitFor);\n  };\n\n  return debounced;\n}\n\ninterface LinguaFlowPageProps {\n  writingMode: 'formal' | 'casual' | 'academic' | 'creative';\n}\n\n// Types are imported from enhanced-document-editor\n\nfunction LinguaFlowPage({ writingMode }: LinguaFlowPageProps) {\n  const { t, getWritingLanguageBase, setWritingLanguageDialect } = useI18n();\n  const { toast } = useToast();\n  const { settings } = useFeatureSettings();\n\n  const [editorValue, setEditorValue] = useState(\"\");\n  const history = useRef<{ past: string[]; present: string; future: string[] }>({ past: [], present: \"\", future: [] });\n  const [canUndo, setCanUndo] = useState(false);\n  const [canRedo, setCanRedo] = useState(false);\n  const isUndoRedoAction = useRef(false);\n\n  const [writingDirection, setWritingDirection] = useState<'ltr' | 'rtl'>('ltr');\n\n  // Document formatting states\n  const [fontSize, setFontSize] = useState(16);\n  const [fontFamily, setFontFamily] = useState(\"'Inter', sans-serif\");\n\n  // Document statistics\n  const [wordCount, setWordCount] = useState(0);\n  const [charCount, setCharCount] = useState(0);\n  const [readingTime, setReadingTime] = useState(0);\n  const [writingScore, setWritingScore] = useState(0);\n\n  // Enhanced suggestions state\n  const [enhancedSuggestions, setEnhancedSuggestions] = useState<EnhancedSuggestion[]>([]);\n  const [isAnalyzingText, setIsAnalyzingText] = useState(false);\n  const [plagiarismResult, setPlagiarismResult] = useState<PlagiarismDetectionOutput | null>(null);\n\n  // Legacy compatibility\n  const [analysisSuggestions, setAnalysisSuggestions] = useState<AnalysisSuggestion[]>([]);\n\n  const plagiarismSuggestions = useMemo<PlagiarismSource[]>((): PlagiarismSource[] => {\n    if (!plagiarismResult || !plagiarismResult.detectedSources) return [];\n\n    return plagiarismResult.detectedSources.map((source, index) => ({\n      id: `plagiarism-${index}-${source.startIndex}`,\n      type: 'style' as const, // Use compatible type\n      category: 'plagiarism' as const,\n      originalSegment: source.plagiarizedSegment,\n      message: `Potential plagiarism detected. Source: ${source.originalSource} (Similarity: ${source.similarityScore}%)`,\n      suggestion: source.originalSource,\n      startIndex: source.startIndex,\n      endIndex: source.endIndex,\n      sourceUrl: source.originalSource,\n      similarityScore: source.similarityScore,\n      confidence: source.similarityScore / 100,\n      severity: source.similarityScore > 80 ? 'high' as const : source.similarityScore > 60 ? 'medium' as const : 'low' as const,\n    }));\n  }, [plagiarismResult]);\n\n  // Convert legacy suggestions to enhanced format\n  const convertedSuggestions = useMemo<EnhancedSuggestion[]>(() => {\n    return analysisSuggestions.map((suggestion) => ({\n      ...suggestion,\n      category: suggestion.type as 'spelling' | 'grammar' | 'style' | 'vocabulary',\n      confidence: 0.8, // Default confidence\n      severity: 'medium' as const,\n    }));\n  }, [analysisSuggestions]);\n\n  // Combine all enhanced suggestions\n  const allEnhancedSuggestions = useMemo(() => {\n    return [...convertedSuggestions, ...plagiarismSuggestions, ...enhancedSuggestions];\n  }, [convertedSuggestions, plagiarismSuggestions, enhancedSuggestions]);\n  \n  // History and Undo/Redo Logic\n  const updateHistory = useCallback((value: string) => {\n    if (isUndoRedoAction.current) {\n        isUndoRedoAction.current = false;\n        return;\n    }\n    const { past, present } = history.current;\n    if (value === present) return;\n    \n    history.current = {\n        past: [...past, present],\n        present: value,\n        future: [],\n    };\n    setCanUndo(history.current.past.length > 0);\n    setCanRedo(history.current.future.length > 0);\n  }, []);\n\n  const debouncedUpdateHistory = useCallback(debounce(updateHistory, 800), [updateHistory]);\n\n  const handleEditorChange = useCallback((value: string) => {\n    setEditorValue(value);\n    debouncedUpdateHistory(value);\n  }, [debouncedUpdateHistory]);\n\n  const handleUndo = useCallback(() => {\n    const { past, present, future } = history.current;\n    if (past.length === 0) return;\n    isUndoRedoAction.current = true;\n    const newPresent = past[past.length - 1];\n    const newPast = past.slice(0, past.length - 1);\n    history.current = {\n        past: newPast,\n        present: newPresent,\n        future: [present, ...future],\n    };\n    setEditorValue(newPresent);\n    setCanUndo(newPast.length > 0);\n    setCanRedo(true);\n  }, []);\n\n  const handleRedo = useCallback(() => {\n      const { past, present, future } = history.current;\n      if (future.length === 0) return;\n      isUndoRedoAction.current = true;\n      const newPresent = future[0];\n      const newFuture = future.slice(1);\n      history.current = {\n          past: [...past, present],\n          present: newPresent,\n          future: newFuture,\n      };\n      setEditorValue(newPresent);\n      setCanUndo(true);\n      setCanRedo(newFuture.length > 0);\n  }, []);\n\n  // Enhanced Text Analysis Logic\n  const requestEnhancedTextAnalysis = useCallback(async (textToAnalyze: string, lang: string) => {\n    if (!textToAnalyze.trim()) {\n      setEnhancedSuggestions([]);\n      setIsAnalyzingText(false);\n      return;\n    }\n    setIsAnalyzingText(true);\n    try {\n      const analysisInput: EnhancedTextAnalysisInput = {\n        text: textToAnalyze,\n        language: lang,\n        focusAreas: ['vocabulary', 'rhythm', 'flow', 'engagement'],\n        avoidRepetition: true,\n      };\n      const response = await fetch('/api/ai/analyze-text-enhanced', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify(analysisInput),\n      });\n\n      if (!response.ok) {\n        throw new Error(`HTTP error! status: ${response.status}`);\n      }\n\n      const result = await response.json();\n      if (result) {\n        // Enhanced suggestions should already have compatible categories\n        const mappedSuggestions = result.suggestions || [];\n        setEnhancedSuggestions(mappedSuggestions);\n        // Update writing score based on enhanced analysis\n        setWritingScore(result.overallScore);\n      }\n    } catch (error) {\n      console.error(\"Error analyzing text for enhanced suggestions:\", error);\n      toast({ titleKey: \"toastErrorTitle\", descriptionKey: \"toastTextAnalysisError\", variant: \"destructive\" });\n      setEnhancedSuggestions([]);\n    } finally {\n      setIsAnalyzingText(false);\n    }\n  }, [toast]);\n\n  // Legacy Text Analysis Logic (for compatibility)\n  const requestTextAnalysis = useCallback(async (textToAnalyze: string, lang: string) => {\n    if (!textToAnalyze.trim()) {\n      setAnalysisSuggestions([]);\n      return;\n    }\n    try {\n      const analysisInput: TextAnalysisInput = {\n        text: textToAnalyze,\n        language: lang,\n      };\n      const response = await fetch('/api/ai/analyze-text', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify(analysisInput),\n      });\n\n      if (!response.ok) {\n        throw new Error(`HTTP error! status: ${response.status}`);\n      }\n\n      const result = await response.json();\n      setAnalysisSuggestions(result.suggestions || []);\n    } catch (error) {\n      console.error(\"Error analyzing text for suggestions:\", error);\n      toast({ titleKey: \"toastErrorTitle\", descriptionKey: \"toastTextAnalysisError\", variant: \"destructive\" });\n      setAnalysisSuggestions([]);\n    }\n  }, [toast]);\n  \n  const debouncedRequestEnhancedTextAnalysis = useCallback(debounce(requestEnhancedTextAnalysis, 1500), [requestEnhancedTextAnalysis]);\n  const debouncedRequestTextAnalysis = useCallback(debounce(requestTextAnalysis, 1500), [requestTextAnalysis]);\n\n  useEffect(() => {\n    // Use enhanced analysis for better suggestions\n    debouncedRequestEnhancedTextAnalysis(editorValue, getWritingLanguageBase());\n    // Also run legacy analysis for compatibility with existing components\n    debouncedRequestTextAnalysis(editorValue, getWritingLanguageBase());\n  }, [editorValue, getWritingLanguageBase, debouncedRequestEnhancedTextAnalysis, debouncedRequestTextAnalysis]);\n\n  // Language Detection Logic\n  const handleLanguageDetection = useCallback(async (text: string) => {\n    if (text.trim().length < 50) return; \n\n    try {\n        const response = await fetch('/api/ai/detect-language', {\n          method: 'POST',\n          headers: {\n            'Content-Type': 'application/json',\n          },\n          body: JSON.stringify({ text }),\n        });\n\n        if (!response.ok) {\n          throw new Error(`HTTP error! status: ${response.status}`);\n        }\n\n        const result = await response.json();\n        const detectedLangCode = result.languageCode;\n        const currentBaseLang = getWritingLanguageBase();\n\n        if (detectedLangCode && detectedLangCode !== 'unknown' && detectedLangCode !== currentBaseLang) {\n            const langInfo = APP_WRITING_LANGUAGES.find(l => l.value === detectedLangCode);\n            if (langInfo) {\n                const newDialect = langInfo.dialects && langInfo.dialects.length > 0\n                    ? langInfo.dialects[0].value\n                    : langInfo.value;\n                \n                setWritingLanguageDialect(newDialect);\n                \n                toast({\n                    titleKey: \"toastInfoTitle\",\n                    descriptionKey: \"toastLanguageSwitched\",\n                    descriptionParams: { language: t(langInfo.labelKey) }\n                });\n            }\n        }\n    } catch (error) {\n        console.error(\"Automatic language detection failed:\", error);\n    }\n  }, [getWritingLanguageBase, setWritingLanguageDialect, toast, t]);\n\n  const debouncedLanguageDetection = useCallback(debounce(handleLanguageDetection, 2000), [handleLanguageDetection]);\n\n  useEffect(() => {\n    const currentBaseLang = getWritingLanguageBase();\n    const langInfo = APP_WRITING_LANGUAGES.find(l => l.value === currentBaseLang);\n    if (langInfo) {\n        setWritingDirection(langInfo.dir || 'ltr');\n    }\n\n    if (settings.enableAutomaticLanguageDetection) {\n      debouncedLanguageDetection(editorValue);\n    }\n  }, [editorValue, debouncedLanguageDetection, settings.enableAutomaticLanguageDetection, getWritingLanguageBase]);\n\n  // Editor stats\n  useEffect(() => {\n    const trimmedValue = editorValue.trim();\n    const words = trimmedValue ? trimmedValue.split(/\\s+/).filter(Boolean) : [];\n    setWordCount(words.length);\n    setCharCount(editorValue.length);\n\n    // Calculate reading time (average 200 words per minute)\n    const readingTimeMinutes = Math.ceil(words.length / 200);\n    setReadingTime(readingTimeMinutes);\n\n    // Calculate writing score based on various factors\n    const lengthScore = Math.min(30, Math.floor(words.length / 10));\n    const issueCount = allEnhancedSuggestions.filter(s => 'category' in s && (s.category === 'spelling' || s.category === 'grammar')).length;\n    const issueDeduction = Math.min(40, issueCount * 5);\n    const score = Math.max(0, Math.min(100, lengthScore + 50 - issueDeduction));\n    setWritingScore(score);\n  }, [editorValue, allEnhancedSuggestions]);\n\n  const handleApplySuggestion = useCallback((suggestion: EnhancedSuggestion) => {\n    let newValue = editorValue;\n    let applied = false;\n    const { suggestion: suggestionText, originalSegment, startIndex, endIndex } = suggestion;\n\n    if (typeof startIndex === 'number' && typeof endIndex === 'number' && endIndex >= startIndex) {\n        const textBefore = editorValue.substring(0, startIndex);\n        const textAfter = editorValue.substring(endIndex);\n        if (editorValue.substring(startIndex, endIndex) === originalSegment) {\n            newValue = textBefore + suggestionText + textAfter;\n            applied = true;\n        }\n    }\n    if (!applied) {\n        // Fallback to first occurrence if indexed replacement fails\n        const firstOccurrenceIndex = editorValue.indexOf(originalSegment);\n        if (firstOccurrenceIndex !== -1) {\n            newValue = editorValue.substring(0, firstOccurrenceIndex) + suggestionText + editorValue.substring(firstOccurrenceIndex + originalSegment.length);\n            applied = true;\n        }\n    }\n    if (applied) {\n        handleEditorChange(newValue);\n        toast({ titleKey: \"toastSuccessTitle\", descriptionKey: \"toastSuggestionAppliedSuccess\" });\n    } else {\n        toast({ titleKey: \"toastErrorTitle\", descriptionKey: \"toastSuggestionApplyError\", variant: \"destructive\" });\n    }\n  }, [editorValue, toast, handleEditorChange]);\n\n  const handleDismissSuggestion = useCallback((suggestionId: string) => {\n    setAnalysisSuggestions(prev => prev.filter(s => s.id !== suggestionId));\n    toast({ titleKey: \"toastInfoTitle\", descriptionKey: \"toastSuggestionDismissed\" });\n  }, [toast]);\n\n  // Legacy wrapper for WritingSuggestionsPanel\n  const handleLegacyApplySuggestion = useCallback((suggestionText: string, originalSegment: string, startIndex?: number, endIndex?: number) => {\n    const legacySuggestion: EnhancedSuggestion = {\n      id: `legacy-${Date.now()}`,\n      type: 'style',\n      category: 'style',\n      message: 'Legacy suggestion',\n      suggestion: suggestionText,\n      originalSegment,\n      startIndex,\n      endIndex,\n      confidence: 0.8,\n      severity: 'medium'\n    };\n    handleApplySuggestion(legacySuggestion);\n  }, [handleApplySuggestion]);\n  \n  const handleInsertGeneratedText = useCallback((textToInsert: string) => {\n    const newText = editorValue.trim() === \"\" ? textToInsert : `${editorValue}\\n\\n${textToInsert}`;\n    handleEditorChange(newText);\n  }, [editorValue, handleEditorChange]);\n\n  const handleApplyRewrite = useCallback((newText: string) => {\n    handleEditorChange(newText);\n  }, [handleEditorChange]);\n\n  // Calculate suggestion counts for status bar\n  const suggestionCounts = useMemo(() => {\n    const counts = {\n      spelling: 0,\n      grammar: 0,\n      style: 0,\n      vocabulary: 0,\n      plagiarism: 0,\n      similarity: 0,\n    };\n\n    allEnhancedSuggestions.forEach(suggestion => {\n      // Type guard to check if suggestion has category property (EnhancedSuggestion)\n      if ('category' in suggestion && suggestion.category) {\n        const category = suggestion.category as keyof typeof counts;\n        if (category in counts) {\n          counts[category]++;\n        }\n      } else {\n        // Handle PlagiarismSource which doesn't have category but has type\n        const type = suggestion.type;\n        if (type === 'spelling' || type === 'grammar' || type === 'style') {\n          counts[type]++;\n        }\n      }\n    });\n\n    return counts;\n  }, [allEnhancedSuggestions]);\n\n  return (\n      <div className=\"flex-1 flex flex-col h-full\">\n        {/* Document Toolbar */}\n        <DocumentToolbar\n          onUndo={handleUndo}\n          onRedo={handleRedo}\n          canUndo={canUndo}\n          canRedo={canRedo}\n          fontSize={fontSize}\n          onFontSizeChange={setFontSize}\n          fontFamily={fontFamily}\n          onFontFamilyChange={setFontFamily}\n          writingMode={writingMode}\n          onWritingModeChange={(mode: string) => {\n            // TODO: Implement writing mode change logic\n            console.log('Writing mode changed to:', mode);\n          }}\n        />\n\n        {/* Main Content Area */}\n        <div className=\"flex-1 grid grid-cols-1 lg:grid-cols-4 gap-6 p-4 md:p-6 overflow-hidden\">\n          {/* Enhanced Document Editor */}\n          <div className=\"lg:col-span-3 h-full\">\n            <EnhancedDocumentEditor\n              value={editorValue}\n              onChange={handleEditorChange}\n              writingMode={writingMode}\n              direction={writingDirection}\n              suggestions={allEnhancedSuggestions.filter(s => 'category' in s && s.category !== 'plagiarism' && s.category !== 'similarity') as EnhancedSuggestion[]}\n              plagiarismSources={plagiarismSuggestions}\n              similaritySources={[]} // TODO: Implement similarity detection\n              onApplySuggestion={handleApplySuggestion}\n              onDismissSuggestion={handleDismissSuggestion}\n              onUndo={handleUndo}\n              onRedo={handleRedo}\n              canUndo={canUndo}\n              canRedo={canRedo}\n              className=\"h-full\"\n            />\n          </div>\n\n          {/* Writing Suggestions Panel */}\n          <div className=\"h-full\">\n            <WritingSuggestionsPanel\n              suggestions={analysisSuggestions}\n              isAnalyzing={isAnalyzingText}\n              onApplySuggestion={handleLegacyApplySuggestion}\n              onDismissSuggestion={handleDismissSuggestion}\n            />\n          </div>\n        </div>\n\n        {/* Document Status Bar */}\n        <DocumentStatusBar\n          wordCount={wordCount}\n          charCount={charCount}\n          readingTime={readingTime}\n          writingScore={writingScore}\n          spellingErrors={suggestionCounts.spelling}\n          grammarErrors={suggestionCounts.grammar}\n          styleIssues={suggestionCounts.style}\n          vocabularyEnhancements={suggestionCounts.vocabulary}\n          plagiarismIssues={suggestionCounts.plagiarism}\n          similarityIssues={suggestionCounts.similarity}\n          language={getWritingLanguageBase()}\n          isAnalyzing={isAnalyzingText}\n        />\n\n        {/* AI Tools Section */}\n        <div className=\"border-t bg-card\">\n          <Tabs defaultValue=\"ai-writer\" className=\"w-full\">\n            <div className=\"flex flex-wrap items-center justify-between gap-4 border-b px-4 py-2\">\n                <TabsList className=\"flex-wrap h-auto\">\n                    <TabsTrigger value=\"ai-writer\"><Wand2 className=\"h-4 w-4 mr-2\"/>{t('aiRewriteTitle')}</TabsTrigger>\n                    <TabsTrigger value=\"content-generator\"><Edit3 className=\"h-4 w-4 mr-2\"/>{t('aiTextGenerationTitle')}</TabsTrigger>\n                    <TabsTrigger value=\"tone-analyzer\"><Gauge className=\"h-4 w-4 mr-2\"/>{t('aiToneAnalysisTitle')}</TabsTrigger>\n                    {settings.enablePlagiarismDetection && <TabsTrigger value=\"plagiarism-detector\"><ShieldCheck className=\"h-4 w-4 mr-2\"/>{t('plagiarismDetectionTitle')}</TabsTrigger>}\n                    <TabsTrigger value=\"ai-writing-detector\"><BrainCircuit className=\"h-4 w-4 mr-2\"/>{t('aiWritingDetectionTitle')}</TabsTrigger>\n                    <TabsTrigger value=\"humanize-text\"><UserCheck className=\"h-4 w-4 mr-2\"/>{t('humanizeAiTextTitle')}</TabsTrigger>\n                </TabsList>\n            </div>\n\n            <div className=\"p-4\">\n              <TabsContent value=\"ai-writer\" className=\"mt-0\">\n                <AiRewriter currentText={editorValue} onApplyRewrite={handleApplyRewrite} writingMode={writingMode} direction={writingDirection} />\n              </TabsContent>\n              <TabsContent value=\"content-generator\" className=\"mt-0\">\n                <AiTextGenerator onInsertText={handleInsertGeneratedText} />\n              </TabsContent>\n              <TabsContent value=\"tone-analyzer\" className=\"mt-0\">\n                {settings.enableToneDetection && <AiToneAnalyzer currentText={editorValue} />}\n              </TabsContent>\n              <TabsContent value=\"plagiarism-detector\" className=\"mt-0\">\n                {settings.enablePlagiarismDetection && <PlagiarismDetector currentText={editorValue} onResult={setPlagiarismResult} />}\n              </TabsContent>\n              <TabsContent value=\"ai-writing-detector\" className=\"mt-0\">\n                <AiWritingDetector currentText={editorValue} />\n              </TabsContent>\n              <TabsContent value=\"humanize-text\" className=\"mt-0\">\n                <HumanizeAiText currentText={editorValue} onInsertText={handleInsertGeneratedText} />\n              </TabsContent>\n            </div>\n          </Tabs>\n        </div>\n      </div>\n  );\n}\n\nexport default function LinguaFlowPageContainer() {\n  return (\n    <AppShell>\n      {(props) => <LinguaFlowPage {...props} />}\n    </AppShell>\n  );\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AASA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAqCA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AA5DA;;;;;;;;;;;;;;;;;AA6DA,4CAA4C;AAE5C,oBAAoB;AACpB,SAAS,SAA4C,IAAO,EAAE,OAAe;IAC3E,IAAI;IAEJ,MAAM,YAAY,CAAC,GAAG;QACpB,aAAa;QACb,UAAU,WAAW,IAAM,QAAQ,OAAO;IAC5C;IAEA,OAAO;AACT;AAMA,mDAAmD;AAEnD,SAAS,eAAe,EAAE,WAAW,EAAuB;IAC1D,MAAM,EAAE,CAAC,EAAE,sBAAsB,EAAE,yBAAyB,EAAE,GAAG,CAAA,GAAA,mIAAA,CAAA,UAAO,AAAD;IACvE,MAAM,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,4HAAA,CAAA,WAAQ,AAAD;IACzB,MAAM,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,kJAAA,CAAA,qBAAkB,AAAD;IAEtC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAyD;QAAE,MAAM,EAAE;QAAE,SAAS;QAAI,QAAQ,EAAE;IAAC;IAClH,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,mBAAmB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IAEhC,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAExE,6BAA6B;IAC7B,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,sBAAsB;IACtB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,6BAA6B;IAC7B,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAwB,EAAE;IACvF,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAoC;IAE3F,uBAAuB;IACvB,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAwB,EAAE;IAEvF,MAAM,wBAAwB,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAsB;QACxD,IAAI,CAAC,oBAAoB,CAAC,iBAAiB,eAAe,EAAE,OAAO,EAAE;QAErE,OAAO,iBAAiB,eAAe,CAAC,GAAG,CAAC,CAAC,QAAQ,QAAU,CAAC;gBAC9D,IAAI,CAAC,WAAW,EAAE,MAAM,CAAC,EAAE,OAAO,UAAU,EAAE;gBAC9C,MAAM;gBACN,UAAU;gBACV,iBAAiB,OAAO,kBAAkB;gBAC1C,SAAS,CAAC,uCAAuC,EAAE,OAAO,cAAc,CAAC,cAAc,EAAE,OAAO,eAAe,CAAC,EAAE,CAAC;gBACnH,YAAY,OAAO,cAAc;gBACjC,YAAY,OAAO,UAAU;gBAC7B,UAAU,OAAO,QAAQ;gBACzB,WAAW,OAAO,cAAc;gBAChC,iBAAiB,OAAO,eAAe;gBACvC,YAAY,OAAO,eAAe,GAAG;gBACrC,UAAU,OAAO,eAAe,GAAG,KAAK,SAAkB,OAAO,eAAe,GAAG,KAAK,WAAoB;YAC9G,CAAC;IACH,GAAG;QAAC;KAAiB;IAErB,gDAAgD;IAChD,MAAM,uBAAuB,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAwB;QACzD,OAAO,oBAAoB,GAAG,CAAC,CAAC,aAAe,CAAC;gBAC9C,GAAG,UAAU;gBACb,UAAU,WAAW,IAAI;gBACzB,YAAY;gBACZ,UAAU;YACZ,CAAC;IACH,GAAG;QAAC;KAAoB;IAExB,mCAAmC;IACnC,MAAM,yBAAyB,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QACrC,OAAO;eAAI;eAAyB;eAA0B;SAAoB;IACpF,GAAG;QAAC;QAAsB;QAAuB;KAAoB;IAErE,8BAA8B;IAC9B,MAAM,gBAAgB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACjC,IAAI,iBAAiB,OAAO,EAAE;YAC1B,iBAAiB,OAAO,GAAG;YAC3B;QACJ;QACA,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,QAAQ,OAAO;QACzC,IAAI,UAAU,SAAS;QAEvB,QAAQ,OAAO,GAAG;YACd,MAAM;mBAAI;gBAAM;aAAQ;YACxB,SAAS;YACT,QAAQ,EAAE;QACd;QACA,WAAW,QAAQ,OAAO,CAAC,IAAI,CAAC,MAAM,GAAG;QACzC,WAAW,QAAQ,OAAO,CAAC,MAAM,CAAC,MAAM,GAAG;IAC7C,GAAG,EAAE;IAEL,MAAM,yBAAyB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,SAAS,eAAe,MAAM;QAAC;KAAc;IAExF,MAAM,qBAAqB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACtC,eAAe;QACf,uBAAuB;IACzB,GAAG;QAAC;KAAuB;IAE3B,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAC7B,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,MAAM,EAAE,GAAG,QAAQ,OAAO;QACjD,IAAI,KAAK,MAAM,KAAK,GAAG;QACvB,iBAAiB,OAAO,GAAG;QAC3B,MAAM,aAAa,IAAI,CAAC,KAAK,MAAM,GAAG,EAAE;QACxC,MAAM,UAAU,KAAK,KAAK,CAAC,GAAG,KAAK,MAAM,GAAG;QAC5C,QAAQ,OAAO,GAAG;YACd,MAAM;YACN,SAAS;YACT,QAAQ;gBAAC;mBAAY;aAAO;QAChC;QACA,eAAe;QACf,WAAW,QAAQ,MAAM,GAAG;QAC5B,WAAW;IACb,GAAG,EAAE;IAEL,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAC3B,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,MAAM,EAAE,GAAG,QAAQ,OAAO;QACjD,IAAI,OAAO,MAAM,KAAK,GAAG;QACzB,iBAAiB,OAAO,GAAG;QAC3B,MAAM,aAAa,MAAM,CAAC,EAAE;QAC5B,MAAM,YAAY,OAAO,KAAK,CAAC;QAC/B,QAAQ,OAAO,GAAG;YACd,MAAM;mBAAI;gBAAM;aAAQ;YACxB,SAAS;YACT,QAAQ;QACZ;QACA,eAAe;QACf,WAAW;QACX,WAAW,UAAU,MAAM,GAAG;IAClC,GAAG,EAAE;IAEL,+BAA+B;IAC/B,MAAM,8BAA8B,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,OAAO,eAAuB;QAC5E,IAAI,CAAC,cAAc,IAAI,IAAI;YACzB,uBAAuB,EAAE;YACzB,mBAAmB;YACnB;QACF;QACA,mBAAmB;QACnB,IAAI;YACF,MAAM,gBAA2C;gBAC/C,MAAM;gBACN,UAAU;gBACV,YAAY;oBAAC;oBAAc;oBAAU;oBAAQ;iBAAa;gBAC1D,iBAAiB;YACnB;YACA,MAAM,WAAW,MAAM,MAAM,iCAAiC;gBAC5D,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;YACvB;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;YAC1D;YAEA,MAAM,SAAS,MAAM,SAAS,IAAI;YAClC,IAAI,QAAQ;gBACV,iEAAiE;gBACjE,MAAM,oBAAoB,OAAO,WAAW,IAAI,EAAE;gBAClD,uBAAuB;gBACvB,kDAAkD;gBAClD,gBAAgB,OAAO,YAAY;YACrC;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,kDAAkD;YAChE,MAAM;gBAAE,UAAU;gBAAmB,gBAAgB;gBAA0B,SAAS;YAAc;YACtG,uBAAuB,EAAE;QAC3B,SAAU;YACR,mBAAmB;QACrB;IACF,GAAG;QAAC;KAAM;IAEV,iDAAiD;IACjD,MAAM,sBAAsB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,OAAO,eAAuB;QACpE,IAAI,CAAC,cAAc,IAAI,IAAI;YACzB,uBAAuB,EAAE;YACzB;QACF;QACA,IAAI;YACF,MAAM,gBAAmC;gBACvC,MAAM;gBACN,UAAU;YACZ;YACA,MAAM,WAAW,MAAM,MAAM,wBAAwB;gBACnD,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;YACvB;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;YAC1D;YAEA,MAAM,SAAS,MAAM,SAAS,IAAI;YAClC,uBAAuB,OAAO,WAAW,IAAI,EAAE;QACjD,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yCAAyC;YACvD,MAAM;gBAAE,UAAU;gBAAmB,gBAAgB;gBAA0B,SAAS;YAAc;YACtG,uBAAuB,EAAE;QAC3B;IACF,GAAG;QAAC;KAAM;IAEV,MAAM,uCAAuC,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,SAAS,6BAA6B,OAAO;QAAC;KAA4B;IACnI,MAAM,+BAA+B,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,SAAS,qBAAqB,OAAO;QAAC;KAAoB;IAE3G,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,+CAA+C;QAC/C,qCAAqC,aAAa;QAClD,sEAAsE;QACtE,6BAA6B,aAAa;IAC5C,GAAG;QAAC;QAAa;QAAwB;QAAsC;KAA6B;IAE5G,2BAA2B;IAC3B,MAAM,0BAA0B,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,OAAO;QACjD,IAAI,KAAK,IAAI,GAAG,MAAM,GAAG,IAAI;QAE7B,IAAI;YACA,MAAM,WAAW,MAAM,MAAM,2BAA2B;gBACtD,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBAAE;gBAAK;YAC9B;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;YAC1D;YAEA,MAAM,SAAS,MAAM,SAAS,IAAI;YAClC,MAAM,mBAAmB,OAAO,YAAY;YAC5C,MAAM,kBAAkB;YAExB,IAAI,oBAAoB,qBAAqB,aAAa,qBAAqB,iBAAiB;gBAC5F,MAAM,WAAW,0HAAA,CAAA,wBAAqB,CAAC,IAAI,CAAC,CAAA,IAAK,EAAE,KAAK,KAAK;gBAC7D,IAAI,UAAU;oBACV,MAAM,aAAa,SAAS,QAAQ,IAAI,SAAS,QAAQ,CAAC,MAAM,GAAG,IAC7D,SAAS,QAAQ,CAAC,EAAE,CAAC,KAAK,GAC1B,SAAS,KAAK;oBAEpB,0BAA0B;oBAE1B,MAAM;wBACF,UAAU;wBACV,gBAAgB;wBAChB,mBAAmB;4BAAE,UAAU,EAAE,SAAS,QAAQ;wBAAE;oBACxD;gBACJ;YACJ;QACJ,EAAE,OAAO,OAAO;YACZ,QAAQ,KAAK,CAAC,wCAAwC;QAC1D;IACF,GAAG;QAAC;QAAwB;QAA2B;QAAO;KAAE;IAEhE,MAAM,6BAA6B,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,SAAS,yBAAyB,OAAO;QAAC;KAAwB;IAEjH,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,kBAAkB;QACxB,MAAM,WAAW,0HAAA,CAAA,wBAAqB,CAAC,IAAI,CAAC,CAAA,IAAK,EAAE,KAAK,KAAK;QAC7D,IAAI,UAAU;YACV,oBAAoB,SAAS,GAAG,IAAI;QACxC;QAEA,IAAI,SAAS,gCAAgC,EAAE;YAC7C,2BAA2B;QAC7B;IACF,GAAG;QAAC;QAAa;QAA4B,SAAS,gCAAgC;QAAE;KAAuB;IAE/G,eAAe;IACf,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,eAAe,YAAY,IAAI;QACrC,MAAM,QAAQ,eAAe,aAAa,KAAK,CAAC,OAAO,MAAM,CAAC,WAAW,EAAE;QAC3E,aAAa,MAAM,MAAM;QACzB,aAAa,YAAY,MAAM;QAE/B,wDAAwD;QACxD,MAAM,qBAAqB,KAAK,IAAI,CAAC,MAAM,MAAM,GAAG;QACpD,eAAe;QAEf,mDAAmD;QACnD,MAAM,cAAc,KAAK,GAAG,CAAC,IAAI,KAAK,KAAK,CAAC,MAAM,MAAM,GAAG;QAC3D,MAAM,aAAa,uBAAuB,MAAM,CAAC,CAAA,IAAK,cAAc,KAAK,CAAC,EAAE,QAAQ,KAAK,cAAc,EAAE,QAAQ,KAAK,SAAS,GAAG,MAAM;QACxI,MAAM,iBAAiB,KAAK,GAAG,CAAC,IAAI,aAAa;QACjD,MAAM,QAAQ,KAAK,GAAG,CAAC,GAAG,KAAK,GAAG,CAAC,KAAK,cAAc,KAAK;QAC3D,gBAAgB;IAClB,GAAG;QAAC;QAAa;KAAuB;IAExC,MAAM,wBAAwB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACzC,IAAI,WAAW;QACf,IAAI,UAAU;QACd,MAAM,EAAE,YAAY,cAAc,EAAE,eAAe,EAAE,UAAU,EAAE,QAAQ,EAAE,GAAG;QAE9E,IAAI,OAAO,eAAe,YAAY,OAAO,aAAa,YAAY,YAAY,YAAY;YAC1F,MAAM,aAAa,YAAY,SAAS,CAAC,GAAG;YAC5C,MAAM,YAAY,YAAY,SAAS,CAAC;YACxC,IAAI,YAAY,SAAS,CAAC,YAAY,cAAc,iBAAiB;gBACjE,WAAW,aAAa,iBAAiB;gBACzC,UAAU;YACd;QACJ;QACA,IAAI,CAAC,SAAS;YACV,4DAA4D;YAC5D,MAAM,uBAAuB,YAAY,OAAO,CAAC;YACjD,IAAI,yBAAyB,CAAC,GAAG;gBAC7B,WAAW,YAAY,SAAS,CAAC,GAAG,wBAAwB,iBAAiB,YAAY,SAAS,CAAC,uBAAuB,gBAAgB,MAAM;gBAChJ,UAAU;YACd;QACJ;QACA,IAAI,SAAS;YACT,mBAAmB;YACnB,MAAM;gBAAE,UAAU;gBAAqB,gBAAgB;YAAgC;QAC3F,OAAO;YACH,MAAM;gBAAE,UAAU;gBAAmB,gBAAgB;gBAA6B,SAAS;YAAc;QAC7G;IACF,GAAG;QAAC;QAAa;QAAO;KAAmB;IAE3C,MAAM,0BAA0B,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QAC3C,uBAAuB,CAAA,OAAQ,KAAK,MAAM,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;QACzD,MAAM;YAAE,UAAU;YAAkB,gBAAgB;QAA2B;IACjF,GAAG;QAAC;KAAM;IAEV,6CAA6C;IAC7C,MAAM,8BAA8B,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC,gBAAwB,iBAAyB,YAAqB;QACrH,MAAM,mBAAuC;YAC3C,IAAI,CAAC,OAAO,EAAE,KAAK,GAAG,IAAI;YAC1B,MAAM;YACN,UAAU;YACV,SAAS;YACT,YAAY;YACZ;YACA;YACA;YACA,YAAY;YACZ,UAAU;QACZ;QACA,sBAAsB;IACxB,GAAG;QAAC;KAAsB;IAE1B,MAAM,4BAA4B,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QAC7C,MAAM,UAAU,YAAY,IAAI,OAAO,KAAK,eAAe,GAAG,YAAY,IAAI,EAAE,cAAc;QAC9F,mBAAmB;IACrB,GAAG;QAAC;QAAa;KAAmB;IAEpC,MAAM,qBAAqB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACtC,mBAAmB;IACrB,GAAG;QAAC;KAAmB;IAEvB,6CAA6C;IAC7C,MAAM,mBAAmB,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QAC/B,MAAM,SAAS;YACb,UAAU;YACV,SAAS;YACT,OAAO;YACP,YAAY;YACZ,YAAY;YACZ,YAAY;QACd;QAEA,uBAAuB,OAAO,CAAC,CAAA;YAC7B,+EAA+E;YAC/E,IAAI,cAAc,cAAc,WAAW,QAAQ,EAAE;gBACnD,MAAM,WAAW,WAAW,QAAQ;gBACpC,IAAI,YAAY,QAAQ;oBACtB,MAAM,CAAC,SAAS;gBAClB;YACF,OAAO;gBACL,mEAAmE;gBACnE,MAAM,OAAO,WAAW,IAAI;gBAC5B,IAAI,SAAS,cAAc,SAAS,aAAa,SAAS,SAAS;oBACjE,MAAM,CAAC,KAAK;gBACd;YACF;QACF;QAEA,OAAO;IACT,GAAG;QAAC;KAAuB;IAE3B,qBACI,8OAAC;QAAI,WAAU;;0BAEb,8OAAC,mJAAA,CAAA,kBAAe;gBACd,QAAQ;gBACR,QAAQ;gBACR,SAAS;gBACT,SAAS;gBACT,UAAU;gBACV,kBAAkB;gBAClB,YAAY;gBACZ,oBAAoB;gBACpB,aAAa;gBACb,qBAAqB,CAAC;oBACpB,4CAA4C;oBAC5C,QAAQ,GAAG,CAAC,4BAA4B;gBAC1C;;;;;;0BAIF,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,mNAAA,CAAA,yBAAsB;4BACrB,OAAO;4BACP,UAAU;4BACV,aAAa;4BACb,WAAW;4BACX,aAAa,uBAAuB,MAAM,CAAC,CAAA,IAAK,cAAc,KAAK,EAAE,QAAQ,KAAK,gBAAgB,EAAE,QAAQ,KAAK;4BACjH,mBAAmB;4BACnB,mBAAmB,EAAE;4BACrB,mBAAmB;4BACnB,qBAAqB;4BACrB,QAAQ;4BACR,QAAQ;4BACR,SAAS;4BACT,SAAS;4BACT,WAAU;;;;;;;;;;;kCAKd,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,+JAAA,CAAA,0BAAuB;4BACtB,aAAa;4BACb,aAAa;4BACb,mBAAmB;4BACnB,qBAAqB;;;;;;;;;;;;;;;;;0BAM3B,8OAAC,yJAAA,CAAA,oBAAiB;gBAChB,WAAW;gBACX,WAAW;gBACX,aAAa;gBACb,cAAc;gBACd,gBAAgB,iBAAiB,QAAQ;gBACzC,eAAe,iBAAiB,OAAO;gBACvC,aAAa,iBAAiB,KAAK;gBACnC,wBAAwB,iBAAiB,UAAU;gBACnD,kBAAkB,iBAAiB,UAAU;gBAC7C,kBAAkB,iBAAiB,UAAU;gBAC7C,UAAU;gBACV,aAAa;;;;;;0BAIf,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,gIAAA,CAAA,OAAI;oBAAC,cAAa;oBAAY,WAAU;;sCACvC,8OAAC;4BAAI,WAAU;sCACX,cAAA,8OAAC,gIAAA,CAAA,WAAQ;gCAAC,WAAU;;kDAChB,8OAAC,gIAAA,CAAA,cAAW;wCAAC,OAAM;;0DAAY,8OAAC,+MAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;4CAAiB,EAAE;;;;;;;kDACnE,8OAAC,gIAAA,CAAA,cAAW;wCAAC,OAAM;;0DAAoB,8OAAC,0MAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;4CAAiB,EAAE;;;;;;;kDAC3E,8OAAC,gIAAA,CAAA,cAAW;wCAAC,OAAM;;0DAAgB,8OAAC,oMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;4CAAiB,EAAE;;;;;;;oCACtE,SAAS,yBAAyB,kBAAI,8OAAC,gIAAA,CAAA,cAAW;wCAAC,OAAM;;0DAAsB,8OAAC,oNAAA,CAAA,cAAW;gDAAC,WAAU;;;;;;4CAAiB,EAAE;;;;;;;kDAC1H,8OAAC,gIAAA,CAAA,cAAW;wCAAC,OAAM;;0DAAsB,8OAAC,sNAAA,CAAA,eAAY;gDAAC,WAAU;;;;;;4CAAiB,EAAE;;;;;;;kDACpF,8OAAC,gIAAA,CAAA,cAAW;wCAAC,OAAM;;0DAAgB,8OAAC,gNAAA,CAAA,YAAS;gDAAC,WAAU;;;;;;4CAAiB,EAAE;;;;;;;;;;;;;;;;;;sCAInF,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,gIAAA,CAAA,cAAW;oCAAC,OAAM;oCAAY,WAAU;8CACvC,cAAA,8OAAC,0IAAA,CAAA,aAAU;wCAAC,aAAa;wCAAa,gBAAgB;wCAAoB,aAAa;wCAAa,WAAW;;;;;;;;;;;8CAEjH,8OAAC,gIAAA,CAAA,cAAW;oCAAC,OAAM;oCAAoB,WAAU;8CAC/C,cAAA,8OAAC,mJAAA,CAAA,kBAAe;wCAAC,cAAc;;;;;;;;;;;8CAEjC,8OAAC,gIAAA,CAAA,cAAW;oCAAC,OAAM;oCAAgB,WAAU;8CAC1C,SAAS,mBAAmB,kBAAI,8OAAC,kJAAA,CAAA,iBAAc;wCAAC,aAAa;;;;;;;;;;;8CAEhE,8OAAC,gIAAA,CAAA,cAAW;oCAAC,OAAM;oCAAsB,WAAU;8CAChD,SAAS,yBAAyB,kBAAI,8OAAC,kJAAA,CAAA,qBAAkB;wCAAC,aAAa;wCAAa,UAAU;;;;;;;;;;;8CAEjG,8OAAC,gIAAA,CAAA,cAAW;oCAAC,OAAM;oCAAsB,WAAU;8CACjD,cAAA,8OAAC,qJAAA,CAAA,oBAAiB;wCAAC,aAAa;;;;;;;;;;;8CAElC,8OAAC,gIAAA,CAAA,cAAW;oCAAC,OAAM;oCAAgB,WAAU;8CAC3C,cAAA,8OAAC,kJAAA,CAAA,iBAAc;wCAAC,aAAa;wCAAa,cAAc;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOxE;AAEe,SAAS;IACtB,qBACE,8OAAC,4IAAA,CAAA,WAAQ;kBACN,CAAC,sBAAU,8OAAC;gBAAgB,GAAG,KAAK;;;;;;;;;;;AAG3C", "debugId": null}}]}